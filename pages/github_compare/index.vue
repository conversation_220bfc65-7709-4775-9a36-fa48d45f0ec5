<template>
  <div class="px-30 h-full" style="margin-top: 4rem">
    <!-- 激活码弹窗 - 使用 ClientOnly 避免 hydration 问题 -->
    <ClientOnly>
      <InviteCodeModal
        v-if="showInviteModal"
        :error="inviteError"
        :loading="inviteLoading"
        @close="showInviteModal = false"
        @submit="handleSubmitActivationCode"
        @waiting-list="onShowWaitingListModal"
      />
      <WaitingListModal
        v-if="showWaitingListModal"
        @close="showWaitingListModal = false"
        @back="onBackToInviteCode"
      />
      <div
        v-if="inviteSuccess"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black/40"
      >
      <div
        class="bg-white rounded-2xl shadow-xl p-8 pt-7 pb-7 w-full max-w-96 relative text-center"
      >
        <div class="text-lg font-semibold text-black mb-6">
          Enter Invite Code
          <button
            class="absolute top-6 right-6 text-gray-400 hover:text-gray-600 transition-colors text-xl"
            @click="inviteSuccess = false"
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 5L5 15M5 5L15 15"
                stroke="#BDBDBD"
                stroke-width="2"
                stroke-linecap="round"
              />
            </svg>
          </button>
        </div>
        <div class="flex justify-center mb-4">
          <div class="bg-green-500 rounded-full w-12 h-12 flex items-center justify-center mx-auto">
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
              <circle cx="16" cy="16" r="16" fill="none" />
              <path
                d="M10 17.5L14 21.5L22 13.5"
                stroke="#fff"
                stroke-width="2.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>
        <div class="text-xl font-bold text-black mb-2">Verification Successful</div>
        <div class="text-base text-gray-700 mb-7">
          Congratulations! Your invitation code has been verified successfully
        </div>
        <button
          class="w-full h-12 bg-black text-white rounded-lg font-semibold text-base transition hover:bg-gray-900"
          @click="goHome"
        >
          Ok
        </button>
      </div>
    </div>
    </ClientOnly>

    <template v-if="!isMounted || loading || isLoadingJson">
      <!-- 骨架屏组件 -->
      <div class="min-h-[60vh]">
        <!-- PK卡片骨架屏 -->
        <div class="relative">
          <div class="grid grid-cols-2 gap-7.5 mb-7.5">
            <!-- 左侧研究者骨架屏 -->
            <div class="bg-[#9BA3C1]/20 dark:bg-gray-600/20 rounded-2xl p-7.5 relative animate-pulse">
              <div class="absolute -top-5 left-7.5">
                <div class="w-15 h-15 rounded-full bg-gray-200/30 dark:bg-gray-600/30 border-4 border-white dark:border-gray-800"></div>
              </div>
              <div class="mt-7.5">
                <div class="h-8 bg-gray-200/30 dark:bg-gray-600/30 rounded w-3/4 mb-3"></div>
                <div class="h-5 bg-gray-200/25 dark:bg-gray-600/25 rounded w-1/2 mb-4"></div>
                <div class="flex flex-wrap gap-2 mb-4">
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-20"></div>
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-24"></div>
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-16"></div>
                </div>
                <div class="space-y-2">
                  <div class="h-6 bg-gray-200/25 dark:bg-gray-600/25 rounded w-5/6"></div>
                  <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-4/6"></div>
                </div>
              </div>
            </div>

            <!-- 右侧研究者骨架屏 -->
            <div class="bg-[#C6A69B]/20 dark:bg-gray-600/20 rounded-2xl p-7.5 relative animate-pulse">
              <div class="absolute -top-5 left-7.5">
                <div class="w-15 h-15 rounded-full bg-gray-200/30 dark:bg-gray-600/30 border-4 border-white dark:border-gray-800"></div>
              </div>
              <div class="mt-7.5">
                <div class="h-8 bg-gray-200/30 dark:bg-gray-600/30 rounded w-3/4 mb-3"></div>
                <div class="h-5 bg-gray-200/25 dark:bg-gray-600/25 rounded w-1/2 mb-4"></div>
                <div class="flex flex-wrap gap-2 mb-4">
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-20"></div>
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-24"></div>
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-16"></div>
                </div>
                <div class="space-y-2">
                  <div class="h-6 bg-gray-200/25 dark:bg-gray-600/25 rounded w-5/6"></div>
                  <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-4/6"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- VS标志骨架屏 -->
          <div class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 z-10">
            <div class="w-20 h-20 rounded-full bg-gray-200/40 dark:bg-gray-600/40 animate-pulse"></div>
          </div>
        </div>

        <Loading :visible="loading" :data="thinking" @update:visible="router.replace('/analysis')" />

        <!-- 雷达图骨架屏 -->
        <div class="bg-white dark:bg-[#1a1a1b] rounded-2xl p-7.5 mb-7.5">
          <div class="animate-pulse">
            <div class="h-8 bg-gray-100/60 dark:bg-gray-600/60 rounded w-1/3 mx-auto mb-8"></div>
            <div class="h-[300px] bg-gray-100/40 dark:bg-gray-600/40 rounded-lg"></div>
          </div>
        </div>

        <!-- 指标对比骨架屏 -->
        <div class="bg-white dark:bg-[#1a1a1b] rounded-2xl p-7.5 mb-7.5">
          <div class="animate-pulse space-y-6">
            <div class="h-12 bg-gray-100/40 dark:bg-gray-600/40 rounded"></div>
            <div class="h-12 bg-gray-100/40 dark:bg-gray-600/40 rounded"></div>
            <div class="h-12 bg-gray-100/40 dark:bg-gray-600/40 rounded"></div>
            <div class="h-12 bg-gray-100/40 dark:bg-gray-600/40 rounded"></div>
          </div>
        </div>

        <!-- 代表项目骨架屏 -->
        <div class="bg-white dark:bg-[#1a1a1b] rounded-2xl p-7.5 mb-7.5">
          <div class="animate-pulse">
            <div class="h-8 bg-gray-100/60 dark:bg-gray-600/60 rounded w-1/4 mx-auto mb-8"></div>
            <div class="grid grid-cols-2 gap-6">
              <div class="space-y-4">
                <div class="h-6 bg-gray-100/50 dark:bg-gray-600/50 rounded w-3/4"></div>
                <div class="h-32 bg-gray-100/40 dark:bg-gray-600/40 rounded"></div>
              </div>
              <div class="space-y-4">
                <div class="h-6 bg-gray-100/50 dark:bg-gray-600/50 rounded w-3/4"></div>
                <div class="h-32 bg-gray-100/40 dark:bg-gray-600/40 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-else-if="pkData">

      <div class="relative bg-cover bg-center bg-no-repeat rounded-xl compare-page-bg">
        <!-- PK卡片部分 -->
        <div class="grid grid-cols-2 gap-7.5 mb-7.5">
          <!-- 左侧GitHub用户 -->
          <div class="rounded-xl p-5 relative flex flex-col items-center text-center min-h-[320px]">
            <div class="absolute -top-5 left-1/2 -translate-x-1/2">
              <div class="flex items-center gap-2">
                <img
                  :src="pkData.user1.user.avatarUrl || pkData.user1.user.avatar_url || '/image/default-avatar.png'"
                  class="w-15 h-15 rounded-full border-4 border-white object-cover"
                />
              </div>
            </div>
            <div class="mt-7.5 w-full flex flex-col justify-between flex-1">
              <div class="flex flex-col">
                <div class="text-white text-xl font-bold">{{ pkData.user1.name }}</div>
                <div class="text-white dark:text-[#C6C6C6] mt-1.5 flex items-start justify-center text-sm max-w-[400px] mx-auto">
                  <SvgIcon name="verified" class="text-4 mr-1.5 mt-0.5 flex-shrink-0" />
                  <span class="text-center line-clamp-2">{{ pkData.user1.user.bio || 'GitHub Developer' }}</span>
                </div>
              </div>
              <div class="mt-auto">
                <div class="flex flex-wrap gap-1.5 mb-3 justify-center items-center min-h-[32px]">
                  <div
                    v-for="(tag, index) in pkData.user1.user.tags?.slice(0, 3) || []"
                    :key="index"
                    class="px-2.5 py-1 bg-[#A1AED2] dark:bg-[#3C4356] rounded-md text-[#262D3F] dark:text-[#FAF9F5] text-xs whitespace-nowrap"
                  >
                    {{ tag }}
                  </div>
                </div>
                <div class="text-white dark:text-[#C6C6C6] text-sm">
                  Wanna compare
                  <span class="text-[#364A83] dark:text-[#FDB852] font-bold">{{
                    pkData.user1.name
                  }}</span>
                  with other GitHub developers?
                </div>
                <div class="text-white dark:text-[#C6C6C6] text-xs mt-1">
                  Just input their GitHub username
                </div>
                <ClientOnly>
                  <motion.div
                    :initial="{ opacity: 0, y: 10 }"
                    :animate="{ opacity: 1, y: 0 }"
                    :transition="{ duration: 0.3, ease: 'easeInOut', delay: 0.2 }"
                    class="f-cer mt-5 mb-4"
                  >
                    <div
                      class="custom-input border rounded-full bg-white border-black min-h-14 w-120 p-1 flex items-center justify-between gap-3 pl-6"
                    >
                      <SearchInput ref="searchInputRef1" placeholder="GitHub username" @enter-search="handleLeftCompare" />
                      <ActionButton :imgSrc="stars" buttonText="Compare" @click="handleLeftCompare" />
                    </div>
                  </motion.div>
                  <template #fallback>
                    <div class="f-cer mt-5 mb-4">
                      <div
                        class="custom-input border rounded-full bg-white border-black min-h-14 w-120 p-1 flex items-center justify-between gap-3 pl-6"
                      >
                        <SearchInput ref="searchInputRef1" placeholder="GitHub username" @enter-search="handleLeftCompare" />
                        <ActionButton :imgSrc="stars" buttonText="Compare" @click="handleLeftCompare" />
                      </div>
                    </div>
                  </template>
                </ClientOnly>
              </div>
            </div>
          </div>
          <!-- 右侧GitHub用户 -->
          <div class="rounded-xl p-5 relative flex flex-col items-center text-center min-h-[320px]">
            <div class="absolute -top-5 left-1/2 -translate-x-1/2">
              <div class="flex items-center gap-2">
                <img
                  :src="pkData.user2.user.avatarUrl || pkData.user2.user.avatar_url || '/image/default-avatar.png'"
                  class="w-15 h-15 rounded-full border-4 border-white object-cover"
                />
              </div>
            </div>
            <div class="mt-7.5 w-full flex flex-col justify-between flex-1">
              <div class="flex flex-col">
                <div class="text-white text-xl font-bold">{{ pkData.user2.name }}</div>
                <div class="text-white dark:text-[#C6C6C6] mt-1.5 flex items-start justify-center text-sm max-w-[400px] mx-auto">
                  <SvgIcon name="verified-brown" class="text-4 mr-1.5 mt-0.5 flex-shrink-0 text-red-500" />
                  <span class="text-center line-clamp-2">{{ pkData.user2.user.bio || 'GitHub Developer' }}</span>
                </div>
              </div>
              <div class="mt-auto">
                <div class="flex flex-wrap gap-1.5 mb-3 justify-center items-center min-h-[32px]">
                  <div
                    v-for="(tag, index) in pkData.user2.user.tags?.slice(0, 3) || []"
                    :key="index"
                    class="px-2.5 py-1 bg-[#E7CDC3] dark:bg-[#413834] rounded-md text-[#7F4832] dark:text-[#FAF9F5] text-xs whitespace-nowrap"
                  >
                    {{ tag }}
                  </div>
                </div>
                <div class="text-white dark:text-[#C6C6C6] text-sm">
                  Wanna compare
                  <span class="text-[#6D4130] dark:text-[#5765F2] font-bold">{{
                    pkData.user2.name
                  }}</span>
                  with other GitHub developers?
                </div>
                <div class="text-white dark:text-[#C6C6C6] text-xs mt-1">
                  Just input their GitHub username
                </div>
                <ClientOnly>
                  <motion.div
                    :initial="{ opacity: 0, y: 10 }"
                    :animate="{ opacity: 1, y: 0 }"
                    :transition="{ duration: 0.3, ease: 'easeInOut', delay: 0.2 }"
                    class="f-cer mt-5 mb-4"
                  >
                    <div
                      class="custom-input border rounded-full bg-white border-black min-h-14 w-120 p-1 flex items-center justify-between gap-3 pl-6"
                    >
                      <SearchInput ref="searchInputRef2" placeholder="GitHub username" @enter-search="handleRightCompare" />
                      <ActionButton :imgSrc="stars" buttonText="Compare" @click="handleRightCompare" />
                    </div>
                  </motion.div>
                  <template #fallback>
                    <div class="f-cer mt-5 mb-4">
                      <div
                        class="custom-input border rounded-full bg-white border-black min-h-14 w-120 p-1 flex items-center justify-between gap-3 pl-6"
                      >
                        <SearchInput ref="searchInputRef2" placeholder="GitHub username" @enter-search="handleRightCompare" />
                        <ActionButton :imgSrc="stars" buttonText="Compare" @click="handleRightCompare" />
                      </div>
                    </div>
                  </template>
                </ClientOnly>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 雷达图部分 -->
      <div class="rounded-2xl p-7.5 mb-7.5">
        <GitHubRadarChart :user1="pkData.user1" :user2="pkData.user2" size="large" />
      </div>

      <div class="flex items-center justify-center mb-10 flex-col gap-4">
        <div
          class="h-13.5 w-[210px] text-4 font-600 text-white bg-[#CB7C5D] dark:bg-[#654D43] dark:border dark:border-[#866457] fx-cer justify-center rounded-full gap-2 cursor-pointer"
          @click="showCompareCard = true"
          >
          <div class="i-proicons:x-twitter wh-5 font-600" data-v-2dc31878=""></div>
          Share
        </div>
      </div>

      <GitHubShareCardCompare
        :show="showCompareCard"
        :is-dark="isDark"
        :user1="pkData.user1"
        :user2="pkData.user2"
        @close="showCompareCard = false"
      />
      <!-- 指标对比和代表项目部分 -->
      <GitHubCompareMetrics :user1="pkData.user1" :user2="pkData.user2" />

      <!-- 比较部分 -->
      <div class="flex flex-col items-center mt-20">
        <div class="text-[56px] clash-semibold font-semibold text-center max-w-[800px] leading-[72.8px]">
          Or compare the profiles<br />
          of two GitHub developers
        </div>
        <div class="flex items-center gap-4 mt-10 w-full max-w-[800px]">
          <div class="flex-1 sty_f_r_end">
            <input
              v-model="user1Input"
              type="text"
              placeholder="GitHub username"
              class="custom-input w-[280px] h-13.5 px-5 rounded-full border border-black bg-white font-bold placeholder-#7C8493 placeholder:font-normal text-black placeholder-gray-300 min-w-0"
              style="outline: none !important; box-shadow: none !important; border-color: black !important;"
              @keyup.enter="handleCompare"
            />
          </div>
          <div class="text-3xl font-bold italic" style="color: #CB7C5D;">VS</div>
          <div class="flex-1">
            <input
              v-model="user2Input"
              type="text"
              placeholder="GitHub username"
              class="custom-input w-[280px] h-13.5 px-5 rounded-full border border-black bg-white font-bold placeholder-#7C8493 placeholder:font-normal text-black placeholder-gray-300 min-w-0"
              style="outline: none !important; box-shadow: none !important; border-color: black !important;"
              @keyup.enter="handleCompare"
            />
          </div>
        </div>
        <button
          @click="handleCompare"
          class="mt-7.5 flex items-center justify-center gap-2 rounded-full bg-black-100 text-white hover:bg-black-100/90 transition-colors w-[210px] h-[54px] dark:border-3 dark:border-white"
        >
          <img src="/image/stars.png" alt="compare" />
          <span class="text-base font-bold">Compare</span>
        </button>
        <div class="text-sm text-gray-500 mt-7.5 mb-20">
          By clicking Compare you agree to our
          <a href="/terms" class="text-primary-100 hover:text-primary-200 underline"
            >terms of service</a
          >
        </div>
      </div>
    </template>
    <template v-else>
      <!-- 兜底展示 -->
      <div class="min-h-[60vh] flex flex-col items-center justify-center">
        <div class="w-20 h-20 rounded-full bg-[#FDF7F7] flex items-center justify-center mb-6">
          <div class="i-carbon:warning-alt text-primary-100 text-3xl"></div>
        </div>
        <div class="text-[32px] font-bold clash-display text-center mb-4">No Data Available</div>
        <div class="text-gray-600 text-center max-w-[500px] mb-10">
          Sorry, we couldn't find the comparison data. Please try comparing other GitHub developers.
        </div>
        <!-- 比较部分 -->
        <div class="w-full max-w-[800px]">
          <div class="text-[42px] font-bold clash-display text-center leading-[1.2] mb-10">
            Compare the profiles<br />
            of two GitHub developers
          </div>
          <div class="flex items-center gap-4">
            <div class="flex-1">
              <input
                v-model="user1Input"
                type="text"
                placeholder="GitHub username"
                class="w-full h-12 px-5 rounded-full border border-black bg-white font-bold placeholder-#7C8493 placeholder:font-normal text-black placeholder-gray-300"
                style="outline: none !important; box-shadow: none !important; border-color: black !important;"
                @keyup.enter="handleCompare"
              />
            </div>
            <div class="text-3xl font-bold italic" style="color: #CB7C5D;">VS</div>
            <div class="flex-1">
              <input
                v-model="user2Input"
                type="text"
                placeholder="GitHub username"
                class="w-full h-12 px-5 rounded-full border border-black bg-white font-bold placeholder-#7C8493 placeholder:font-normal text-black placeholder-gray-300"
                style="outline: none !important; box-shadow: none !important; border-color: black !important;"
                @keyup.enter="handleCompare"
              />
            </div>
          </div>
          <div class="flex justify-center mt-7.5">
            <button
              @click="handleCompare"
              class="flex items-center justify-center gap-2 rounded-full bg-black-100 text-white hover:bg-black-100/90 transition-colors w-[210px] h-[54px] dark:border-3 dark:border-white"
            >
              <img src="/image/stars.png" alt="compare" />
              <span class="text-base font-bold">Compare</span>
            </button>
          </div>
          <div class="text-sm text-gray-500 text-center mt-7.5 mb-20">
            By clicking Compare you agree to our
            <a href="/terms" class="text-primary-100 hover:text-primary-200 underline"
              >terms of service</a
            >
          </div>
        </div>
      </div>
    </template>

    <!-- 隐藏的分享卡片用于自动生成OG图片 -->
    <!-- 使用正确的隐藏方式：transform: translateX(-100%) -->
    <div
      v-if="pkData && !ogImageGenerated"
      class="hidden-render-container"
      :style="{
        position: 'fixed',
        top: '0',
        left: '0',
        transform: 'translateX(-100%)',
        zIndex: '-1',
        pointerEvents: 'none'
      }"
    >
      <GitHubShareCardCompare
        ref="hiddenShareCardRef"
        :show="true"
        :user1="pkData.user1"
        :user2="pkData.user2"
        :is-dark="isDark"
        @close="() => {}"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import stars from '@/assets/image/stars.png'
  import { getSiteUrl } from '~/utils/request'
  import { useRoute, useRouter } from 'vue-router'
  import { useEventStream } from '@/composables/useEventStream'
  import { motion } from "motion-v"
  import GitHubRadarChart from '@/components/GitHubRadarChart.vue'
  import GitHubCompareMetrics from '@/components/GitHubCompareMetrics.vue'
  import GitHubShareCardCompare from '@/components/GitHubShareCardCompare/index.vue'
  import SearchInput from '@/components/SearchInput/index.vue'
  import ActionButton from '@/components/ActionButton/index.vue'
  import SvgIcon from '@/components/SvgIcon/index.vue'
  import { ref, onMounted, watch, onUnmounted, nextTick } from 'vue'
  import InviteCodeModal from '@/components/InviteCodeModal.vue'
  import WaitingListModal from '@/components/WaitingListModal.vue'
  import Loading from '@/components/Loading/index.vue'
  import { submitActivationCode } from '~/api'
  import { getPredictableCompareOgImageUrl, checkCompareOgImageExists, uploadFileToS3 } from '~/utils'
  import html2canvas from 'html2canvas-pro'

  definePageMeta({
    middleware: 'auth',
  })

  // 从 URL 获取用户名用于初始 SEO
  const route = useRoute()
  const user1 = route.query.user1 as string
  const user2 = route.query.user2 as string

  // 设置初始 SEO meta（在服务端渲染时生效）
  if (user1 && user2) {
    const initialTitle = `${user1} vs ${user2} - GitHub Developer Comparison | DINQ`
    const initialDescription = `Compare GitHub developers ${user1} and ${user2}. Analyze their coding skills, project contributions, and development expertise side by side.`

    // 使用可预测的OG图片URL（优先）
    const predictableOgImageUrl = getPredictableCompareOgImageUrl(user1, user2)

    useSeoMeta({
      title: initialTitle,
      description: initialDescription,
      keywords: `${user1}, ${user2}, GitHub Comparison, Developer Comparison, GitHub Analysis, Developer Skills, Code Comparison`,

      // Open Graph - 优先使用可预测的OG图片URL
      ogTitle: initialTitle,
      ogDescription: initialDescription,
      ogType: 'website',
      ogUrl: `https://dinq.io/github_compare?user1=${user1}&user2=${user2}`,
      ogImage: predictableOgImageUrl,

      // Twitter Card - 优先使用可预测的OG图片URL
      twitterCard: 'summary_large_image',
      twitterTitle: initialTitle,
      twitterDescription: initialDescription,
      twitterImage: predictableOgImageUrl,
    })

    useHead({
      title: initialTitle,
      meta: [
        {
          name: 'robots',
          content: 'index, follow'
        },
        {
          name: 'theme-color',
          content: '#CB7C5D'
        }
      ],
      link: [
        {
          rel: 'canonical',
          href: `https://dinq.io/github_compare?user1=${user1}&user2=${user2}`
        }
      ]
    })
  }

  // 动态 SEO 元数据更新（数据加载后的增强版本）
  const updateCompareSeoMeta = (data: any) => {
    const user1Name = data.user1.name || data.user1.user?.login || 'Developer 1'
    const user2Name = data.user2.name || data.user2.user?.login || 'Developer 2'

    const title = `${user1Name} vs ${user2Name} - GitHub Developer Comparison | DINQ`
    const description = `Compare GitHub developers ${user1Name} and ${user2Name}. Analyze their coding skills, project contributions, and development expertise side by side.`

    // 获取两个开发者的主要编程语言
    const user1Languages = Object.keys(data.user1.code_contribution?.languages || {}).slice(0, 3)
    const user2Languages = Object.keys(data.user2.code_contribution?.languages || {}).slice(0, 3)
    const allLanguages = [...new Set([...user1Languages, ...user2Languages])]

    const keywords = [
      user1Name,
      user2Name,
      'GitHub Comparison',
      'Developer Comparison',
      'GitHub Analysis',
      'Developer Skills',
      'Code Comparison',
      ...allLanguages
    ].join(', ')

    useSeoMeta({
      title,
      description,
      keywords,

      // Open Graph
      ogTitle: title,
      ogDescription: description,
      ogType: 'website',
      ogUrl: `https://dinq.io/github_compare?user1=${data.user1.user?.login}&user2=${data.user2.user?.login}`,

      // Twitter Card
      twitterCard: 'summary_large_image',
      twitterTitle: title,
      twitterDescription: description,

      // 额外的 meta 标签
      author: 'DINQ',
    })

    // 设置页面标题
    useHead({
      title,
      meta: [
        {
          name: 'robots',
          content: 'index, follow'
        },
        {
          name: 'theme-color',
          content: '#CB7C5D'
        }
      ],
      link: [
        {
          rel: 'canonical',
          href: `https://dinq.io/github_compare?user1=${data.user1.user?.login}&user2=${data.user2.user?.login}`
        }
      ]
    })
  }

  // const route = useRoute() // 已在上面声明
  const router = useRouter()
  const { currentUser } = useFirebaseAuth()

  // Import what we need - connectWithObj and thinking are for SSE fallback only
  const { thinking, loading, connectWithObj, limitInfo, pkData } = useEventStream()

  // Note: thinking is kept for SSE fallback scenarios
  // reportDataInfo removed since we handle direct HTTP responses now

  const user1Input = ref('')
  const user2Input = ref('')

  // 为顶部搜索框添加独立的ref
  const searchInputRef1 = ref()
  const searchInputRef2 = ref()

  // 移除未使用的变量
  // const isAnalyzing = ref(false)
  // const analysisProgress = ref(0)
  let progressInterval: NodeJS.Timeout | null = null

  const isLoadingJson = ref(false)

  // 邀请码相关状态
  const showInviteModal = ref(false)
  const showWaitingListModal = ref(false)
  const inviteError = ref('')
  const inviteLoading = ref(false)
  const inviteSuccess = ref(false)

  // 分享卡片状态
  const showCompareCard = ref(false)

  // 主题状态
  const isDark = ref(false)

  // OG图片生成相关变量
  const ogImageGenerated = ref(false)
  const ogImageUrl = ref('')
  const hiddenShareCardRef = ref()

  // 检测深色模式 - 仅在客户端执行
  const updateDarkMode = () => {
    if (import.meta.client) {
      isDark.value = document.documentElement.classList.contains('dark')
    }
  }

  // 添加客户端挂载状态
  const isMounted = ref(false)

  // 存储清理函数的引用
  let currentSimulationCleanup: (() => void) | null = null

  onMounted(() => {
    isMounted.value = true
    updateDarkMode()
    const observer = new MutationObserver(updateDarkMode)
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })

    // 延迟执行以确保认证状态已初始化
    nextTick(async () => {
      if (route.query.user1 && route.query.user2) {
        // 重置加载状态
        loading.value = true
        isLoadingJson.value = false
        pkData.value = null
        ogImageGenerated.value = false
        ogImageUrl.value = ''

        // 检查是否已有OG图片
        checkExistingCompareOgImage(route.query.user1 as string, route.query.user2 as string)

        // 使用可复用函数获取数据
        await fetchComparisonData(route.query.user1 as string, route.query.user2 as string)
      }
    })
  })

  // 处理左侧搜索框的比较请求（左侧用户固定，用户输入右侧用户）
  const handleLeftCompare = async (query?: string) => {
    const newUser = query || searchInputRef1.value?.searchValue

    if (!newUser?.trim() || !pkData.value?.user1?.name) return

    const user1Name = pkData.value.user1.name

    // 更新路由参数：左侧用户保持不变，右侧用户使用用户输入
    router.replace({
      path: '/github_compare',
      query: {
        user1: user1Name,
        user2: newUser.trim(),
      },
    })

    // 显示分析中弹窗
    loading.value = true
    isLoadingJson.value = false
    pkData.value = null

    // 使用可复用函数获取数据
    await fetchComparisonData(user1Name, newUser.trim())
  }

  // 处理右侧搜索框的比较请求（右侧用户固定，用户输入左侧用户）
  const handleRightCompare = async (query?: string) => {
    const newUser = query || searchInputRef2.value?.searchValue

    if (!newUser?.trim() || !pkData.value?.user2?.name) return

    const user2Name = pkData.value.user2.name

    // 更新路由参数：左侧用户使用用户输入，右侧用户保持不变
    router.replace({
      path: '/github_compare',
      query: {
        user1: newUser.trim(),
        user2: user2Name,
      },
    })

    // 显示分析中弹窗
    loading.value = true
    isLoadingJson.value = false
    pkData.value = null

    // 使用可复用函数获取数据
    await fetchComparisonData(newUser.trim(), user2Name)
  }

  // 处理底部搜索框的比较请求
  const handleCompare = async () => {
    if (!user1Input.value || !user2Input.value) return

    // 更新路由参数
    router.replace({
      path: '/github_compare',
      query: {
        user1: user1Input.value,
        user2: user2Input.value,
      },
    })

    // 显示分析中弹窗
    loading.value = true
    isLoadingJson.value = false
    pkData.value = null

    // 使用可复用函数获取数据
    await fetchComparisonData(user1Input.value, user2Input.value)
  }

  // 激活码相关函数
  const handleSubmitActivationCode = async (code: string) => {
    inviteLoading.value = true
    inviteError.value = ''

    try {
      const result = await submitActivationCode(
        '/api/activation-code/verify',
        { code },
        { headers: { userid: currentUser.value?.uid } }
      )

      if (result.data.success) {
        inviteSuccess.value = true
        showInviteModal.value = false
      } else {
        inviteError.value = 'Invalid activation code'
      }
    } catch (error) {
      inviteError.value = 'Failed to verify activation code'
    } finally {
      inviteLoading.value = false
    }
  }

  const onShowWaitingListModal = () => {
    showInviteModal.value = false
    showWaitingListModal.value = true
  }

  const onBackToInviteCode = () => {
    showWaitingListModal.value = false
    showInviteModal.value = true
  }

  const goHome = () => {
    inviteSuccess.value = false
    router.push('/')
  }



  // 监听限制信息
  watch(limitInfo, data => {
    if (data && data.errorType === 'limit') {
      showInviteModal.value = true
      inviteError.value = ''
      inviteLoading.value = false
    }
  })

  // Remove reportDataInfo watcher since we handle direct HTTP responses now
  // This was only needed for SSE-based endpoints that return JSON URLs

  // 监听 pkData 变化，确保加载状态正确
  watch(pkData, (newData) => {
    if (newData) {
      // 如果直接收到 pkData，确保加载状态被正确设置
      loading.value = false
      isLoadingJson.value = false
    }
  })

  // 模拟加载文本的函数
  const simulateLoadingText = (user1: string, user2: string) => {
    thinking.value = []

    const loadingSteps = [
      `Analyzing ${user1}'s GitHub profile...`,
      `Fetching ${user1}'s repositories and contributions...`,
      `Analyzing ${user2}'s GitHub profile...`,
      `Fetching ${user2}'s repositories and contributions...`,
      'Comparing coding patterns and project complexity...',
      'Analyzing contribution frequency and consistency...',
      'Evaluating repository impact and collaboration...',
      'Generating comprehensive comparison report...'
    ]

    let currentStep = 0

    // 添加第一步
    thinking.value.push(loadingSteps[currentStep])
    currentStep++

    // 每1.5秒添加一个新步骤
    const interval = setInterval(() => {
      if (currentStep < loadingSteps.length && loading.value) {
        thinking.value.push(loadingSteps[currentStep])
        currentStep++
      } else {
        clearInterval(interval)
      }
    }, 1500)

    // 清理函数
    return () => clearInterval(interval)
  }

  // 可复用的 API 调用函数
  const fetchComparisonData = async (user1: string, user2: string) => {
    // 清理之前的模拟（如果存在）
    if (currentSimulationCleanup) {
      currentSimulationCleanup()
    }

    // 开始模拟加载文本
    currentSimulationCleanup = simulateLoadingText(user1, user2)

    try {
      const runtimeConfig = useRuntimeConfig()
      const apiBaseUrl = runtimeConfig.public.apiBase || 'https://api.dinq.io'
      const response = await fetch(`${apiBaseUrl}/api/github/compare`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Userid': currentUser.value?.uid || '',
        },
        body: JSON.stringify({ user1, user2 }),
      })

      const contentType = response.headers.get('content-type')

      if (contentType && contentType.includes('application/json')) {
        const data = await response.json()

        if (data.success && data.data && data.data.user1 && data.data.user2) {
          pkData.value = data.data
          loading.value = false
          isLoadingJson.value = false

          // 更新 SEO 元数据
          updateCompareSeoMeta(data.data)

          // 自动生成OG图片
          nextTick(() => {
            generateCompareOgImage(user1, user2)
          })

          // 停止模拟文本
          if (currentSimulationCleanup) {
            currentSimulationCleanup()
            currentSimulationCleanup = null
          }
          return true // Success
        } else {
          throw new Error('Invalid response format')
        }
      } else {
        // Not JSON, fall back to SSE
        response.body?.cancel()
        // 停止模拟文本，SSE会提供真实的thinking数据
        if (currentSimulationCleanup) {
          currentSimulationCleanup()
          currentSimulationCleanup = null
        }
        connectWithObj({ user1, user2 }, '/api/github/compare', { Userid: currentUser.value?.uid || '' })
        return true // SSE fallback initiated
      }
    } catch (error) {
      console.error('Error fetching comparison data:', error)
      // 停止模拟文本
      if (currentSimulationCleanup) {
        currentSimulationCleanup()
        currentSimulationCleanup = null
      }
      // Fall back to SSE on error
      connectWithObj({ user1, user2 }, '/api/github/compare', { Userid: currentUser.value?.uid || '' })
      return false
    }
  }

  // 生成比较页面OG图片
  const generateCompareOgImage = async (user1: string, user2: string) => {
    if (ogImageGenerated.value || !import.meta.client) return

    try {
      console.log('开始生成比较页面OG图片...')

      // 等待Vue的响应式更新完成
      await nextTick()

      const shareCardElement = document.querySelector('[data-card-id="share-card-github-compare"]')
      if (!shareCardElement) {
        console.error('未找到比较分享卡片元素')
        return
      }

      // 等待图片资源加载完成
      const images = shareCardElement.getElementsByTagName('img')
      const imagePromises = [...images].map(img => {
        if (img.complete) return Promise.resolve()
        return new Promise(resolve => {
          img.onload = resolve
          img.onerror = resolve // 即使图片加载失败也继续
        })
      })
      await Promise.all(imagePromises)

      // 等待自定义字体加载完成
      if (document.fonts) {
        await document.fonts.ready
      }

      // 给浏览器额外的渲染时间
      await new Promise(resolve => setTimeout(resolve, 500))

      console.log('所有资源加载完成，开始截图...')

      // 使用html2canvas生成图片
      const canvas = await html2canvas(shareCardElement as HTMLElement, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        logging: true,
        imageTimeout: 15000,
        foreignObjectRendering: false,
        scrollX: 0,
        scrollY: 0,
        onclone: (clonedDoc) => handleCompareImageProcessing(clonedDoc, isDark.value)
      })

      // 转换为blob
      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => resolve(blob!), 'image/png', 1.0)
      })

      // 生成固定格式的文件名
      const fileName = `github-compare-${user1}-vs-${user2}-latest.png`

      // 上传到S3
      const publicUrl = await uploadFileToS3(blob, 'image/png', fileName)

      console.log('比较页面OG图片上传成功:', publicUrl)

      // 保存URL到数据库
      await $fetch('/api/github/save-compare-og-image', {
        method: 'POST',
        body: {
          user1,
          user2,
          ogImageUrl: publicUrl
        }
      })

      // 更新本地状态
      ogImageUrl.value = publicUrl
      ogImageGenerated.value = true

      // 动态更新meta标签
      updateSeoMetaWithOgImage(publicUrl)

    } catch (error) {
      console.error('生成比较页面OG图片失败:', error)
    }
  }

  // 比较页面图片处理逻辑
  const handleCompareImageProcessing = (clonedDoc: Document, isDarkMode: boolean) => {
    const clonedElement = clonedDoc.querySelector('[data-card-id="share-card-github-compare"]')
    if (!clonedElement) return

    // 1. 替换按钮为版权信息
    const buttonContainer = clonedElement.querySelector('[data-action-buttons]')
    if (buttonContainer) {
      const copyrightDiv = clonedDoc.createElement('div')
      copyrightDiv.style.cssText = 'font-size: 12px; color: #666; font-weight: 400; white-space: nowrap;'
      copyrightDiv.textContent = 'Copyright @ 2025 DINQ Inc. All rights reserved'

      const qrCode = clonedDoc.createElement('img')
      qrCode.src = '/image/qrcode.png'
      qrCode.alt = 'QR Code'
      qrCode.style.cssText = 'width: 48px; height: 48px; flex-shrink: 0; margin-left: 8px;'

      const bottomRightContainer = clonedDoc.createElement('div')
      bottomRightContainer.style.cssText = 'position: absolute; bottom: 16px; right: 32px; display: flex; align-items: center; gap: 8px; z-index: 10;'
      bottomRightContainer.appendChild(copyrightDiv)
      bottomRightContainer.appendChild(qrCode)

      buttonContainer.parentNode?.replaceChild(bottomRightContainer, buttonContainer)
    }

    // 2. 替换SVG图标为PNG图片（复用现有逻辑）
    const svgIconElements = clonedElement.querySelectorAll('svg.svg-icon')
    svgIconElements.forEach((svgEl) => {
      const svgElement = svgEl as SVGElement
      const useElement = svgElement.querySelector('use')
      if (!useElement) return

      const iconId = useElement.getAttribute('xlink:href') || useElement.getAttribute('href')
      if (!iconId) return

      const imgElement = clonedDoc.createElement('img')

      // 图标映射表
      const iconMappings: Record<string, { src: string; alt: string; className: string }> = {
        '#icon-verified': { src: '/image/sharecard/github-verify.png', alt: 'verified', className: 'w-4 h-4 mt-0.5 flex-shrink-0' },
        '#icon-research': { src: '/image/sharecard/overview.png', alt: 'overview', className: 'w-5 h-5' },
        '#icon-add1': { src: '/image/sharecard/additions.png', alt: 'additions', className: 'w-5 h-5' },
        '#icon-trash-bin': { src: '/image/sharecard/deletions.png', alt: 'deletions', className: 'w-5 h-5' },
        '#icon-project': { src: '/image/sharecard/highlight.png', alt: 'highlight', className: 'w-5 h-5' },
        '#icon-stars': { src: '/image/sharecard/stars.png', alt: 'stars', className: 'w-4 h-4' },
        '#icon-forks': { src: '/image/sharecard/forks.png', alt: 'forks', className: 'w-4 h-4' },
        '#icon-growth': { src: '/image/sharecard/marketvalue.png', alt: 'market value', className: 'w-5 h-5' },
        '#icon-growth-investing': { src: '/image/sharecard/yoe.png', alt: 'yoe', className: 'w-5 h-5' }
      }

      const mapping = iconMappings[iconId]
      if (mapping) {
        imgElement.src = mapping.src
        imgElement.alt = mapping.alt
        imgElement.className = mapping.className
        svgElement.parentNode?.replaceChild(imgElement, svgElement)
      }
    })

    // 3. 修复头像路径
    const avatarImages = clonedElement.querySelectorAll('img[src*="@/assets/image/avator.png"]')
    avatarImages.forEach(img => {
      const imgEl = img as HTMLImageElement
      imgEl.src = '/image/avator.png'
    })

    // 4. 处理雷达图Canvas（关键：Chart.js Canvas处理）
    fixRadarChartCanvas(clonedElement)

    // 4. 修复标签样式（关键：比较页面特有的标签处理）
    fixCompareTagStyles(clonedElement, isDarkMode)

    // 5. 修复比较页面特有的样式
    fixCompareSpecificStyles(clonedElement, isDarkMode)

    // 6. 修复通用样式（复用现有逻辑）
    fixCompareBackgroundImages(clonedElement, isDarkMode)
    fixCompareTextColors(clonedElement, isDarkMode)
    fixCompareGlassEffects(clonedElement, isDarkMode)
    fixCompareHighlightCards(clonedElement, isDarkMode)
  }

  // 修复雷达图Canvas（关键：Chart.js Canvas处理）
  const fixRadarChartCanvas = (clonedElement: Element) => {
    // 查找雷达图容器
    const radarCharts = clonedElement.querySelectorAll('.radar-chart')

    radarCharts.forEach(radarChart => {
      const canvas = radarChart.querySelector('canvas')
      if (canvas) {
        try {
          // 获取原始Canvas的数据URL
          const originalCanvas = canvas as HTMLCanvasElement
          const dataURL = originalCanvas.toDataURL('image/png')

          // 创建一个img元素替换canvas
          const img = document.createElement('img')
          img.src = dataURL
          img.style.width = originalCanvas.style.width || `${originalCanvas.width}px`
          img.style.height = originalCanvas.style.height || `${originalCanvas.height}px`
          img.style.maxWidth = '100%'
          img.style.maxHeight = '100%'

          // 替换canvas为img
          originalCanvas.parentNode?.replaceChild(img, originalCanvas)

          console.log('雷达图Canvas已转换为图片')
        } catch (error) {
          console.error('转换雷达图Canvas失败:', error)

          // 如果转换失败，尝试设置Canvas的样式以确保html2canvas能正确处理
          const canvasEl = canvas as HTMLCanvasElement
          canvasEl.style.backgroundColor = 'transparent'
          canvasEl.style.display = 'block'
        }
      }
    })
  }

  // 修复比较页面标签样式（关键功能）
  const fixCompareTagStyles = (clonedElement: Element, isDarkMode: boolean) => {
    const allTags = clonedElement.querySelectorAll('.tag-component')

    allTags.forEach((tag) => {
      const tagEl = tag as HTMLElement

      // 检查标签的父容器来判断是左侧还是右侧
      let isLeftSide = false
      let isRightSide = false

      // 向上查找父元素，寻找justify-end（左侧）或justify-start（右侧）
      let parent = tagEl.parentElement
      while (parent && parent !== clonedElement) {
        const parentClasses = parent.className

        if (parentClasses.includes('justify-end')) {
          isLeftSide = true
          break
        }
        if (parentClasses.includes('justify-start')) {
          isRightSide = true
          break
        }
        parent = parent.parentElement
      }

      if (isDarkMode) {
        if (isLeftSide) {
          // share-blue variant 深色模式样式（左侧用户）
          tagEl.style.backgroundColor = '#3C4356'
          tagEl.style.borderColor = '#7F8EB7'
          tagEl.style.color = '#C2C5CE'
          tagEl.style.borderWidth = '0.5px'
          tagEl.style.borderStyle = 'solid'
        } else if (isRightSide) {
          // share-orange variant 深色模式样式（右侧用户）
          tagEl.style.backgroundColor = '#413834'
          tagEl.style.borderColor = '#71635E'
          tagEl.style.color = '#E1BCAD'
          tagEl.style.borderWidth = '0.5px'
          tagEl.style.borderStyle = 'solid'
        }
      } else {
        if (isLeftSide) {
          // share-blue variant 亮色模式样式（左侧用户）
          tagEl.style.backgroundColor = '#EEF1F9'
          tagEl.style.borderColor = '#7F8EB7'
          tagEl.style.color = '#7F8EB7'
          tagEl.style.borderWidth = '0.5px'
          tagEl.style.borderStyle = 'solid'
        } else if (isRightSide) {
          // share-orange variant 亮色模式样式（右侧用户）
          tagEl.style.backgroundColor = '#FBEAE3'
          tagEl.style.borderColor = '#CB7C5D'
          tagEl.style.color = '#CB7C5D'
          tagEl.style.borderWidth = '0.5px'
          tagEl.style.borderStyle = 'solid'
        }
      }
    })
  }

  // 比较页面特有样式修复
  const fixCompareSpecificStyles = (clonedElement: Element, isDarkMode: boolean) => {
    // 修复VS分隔符样式
    const vsSeparators = clonedElement.querySelectorAll('.vs-separator, [class*="vs"]')
    vsSeparators.forEach(separator => {
      const sepEl = separator as HTMLElement
      if (isDarkMode) {
        sepEl.style.color = '#FAF9F5'
        sepEl.style.backgroundColor = '#27282D'
      } else {
        sepEl.style.color = '#1F2937'
        sepEl.style.backgroundColor = '#F9FAFB'
      }
    })

    // 修复对比卡片的边框
    const compareCards = clonedElement.querySelectorAll('.compare-card, [class*="compare"]')
    compareCards.forEach(card => {
      const cardEl = card as HTMLElement
      if (isDarkMode) {
        cardEl.style.borderColor = '#27282D'
      } else {
        cardEl.style.borderColor = '#E5E7EB'
      }
    })
  }

  // 复用单用户页面的样式修复函数（适配比较页面）
  const fixCompareBackgroundImages = (clonedElement: Element, isDarkMode: boolean) => {
    const customBgCards = clonedElement.querySelectorAll('.custom-bg')
    customBgCards.forEach((card) => {
      const cardEl = card as HTMLElement

      const isAdditionsCard = cardEl.textContent?.includes('Additions') || cardEl.querySelector('img[alt="additions"]')
      const isDeletionsCard = cardEl.textContent?.includes('Deletions') || cardEl.querySelector('img[alt="deletions"]')
      const isMarketValueCard = cardEl.textContent?.includes('Market Value') || cardEl.querySelector('img[alt="market value"]')
      const isYoECard = cardEl.textContent?.includes('YoE') || cardEl.querySelector('img[alt="yoe"]')

      if (isDarkMode) {
        cardEl.style.setProperty('background-image', 'url(/image/sharecard/Group2xdark.png)', 'important')
      } else {
        if (isAdditionsCard || isMarketValueCard) {
          cardEl.style.setProperty('background-image', 'url(/image/sharecard/Group2x1.png)', 'important')
        } else if (isDeletionsCard || isYoECard) {
          cardEl.style.setProperty('background-image', 'url(/image/sharecard/Group2x.png)', 'important')
        }
      }

      cardEl.style.setProperty('background-repeat', 'no-repeat', 'important')
      cardEl.style.setProperty('background-size', 'contain', 'important')
      cardEl.style.setProperty('background-position', 'left top', 'important')
    })
  }

  const fixCompareTextColors = (clonedElement: Element, isDarkMode: boolean) => {
    const customBgCards = clonedElement.querySelectorAll('.custom-bg')
    customBgCards.forEach((card) => {
      const cardEl = card as HTMLElement

      const isAdditionsCard = cardEl.textContent?.includes('Additions')
      const isDeletionsCard = cardEl.textContent?.includes('Deletions')
      const isMarketValueCard = cardEl.textContent?.includes('Market Value')
      const isYoECard = cardEl.textContent?.includes('YoE')

      const textElements = cardEl.querySelectorAll('[class*="text-[#"]')
      textElements.forEach(textEl => {
        const textElement = textEl as HTMLElement
        if (isDarkMode) {
          if (isAdditionsCard || isMarketValueCard) {
            textElement.style.setProperty('color', '#A5AEC6', 'important')
          } else if (isDeletionsCard || isYoECard) {
            textElement.style.setProperty('color', '#B28383', 'important')
          }
        } else {
          if (isAdditionsCard || isMarketValueCard) {
            textElement.style.setProperty('color', '#5F6D94', 'important')
          } else if (isDeletionsCard || isYoECard) {
            textElement.style.setProperty('color', '#CB7C5D', 'important')
          }
        }
      })
    })
  }

  const fixCompareGlassEffects = (clonedElement: Element, isDarkMode: boolean) => {
    const glassCards = clonedElement.querySelectorAll('[style*="backdrop-filter: blur(14px)"]')
    glassCards.forEach(card => {
      const cardEl = card as HTMLElement
      if (isDarkMode) {
        cardEl.style.backgroundColor = 'rgba(20, 20, 21, 0.85)'
        cardEl.style.backdropFilter = 'none'
        cardEl.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.3)'
      } else {
        cardEl.style.backgroundColor = 'rgba(255, 255, 255, 0.85)'
        cardEl.style.backdropFilter = 'none'
        cardEl.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.1)'
      }
    })
  }

  const fixCompareHighlightCards = (clonedElement: Element, isDarkMode: boolean) => {
    const highlightCards = clonedElement.querySelectorAll('.border-l-4')
    highlightCards.forEach(card => {
      const cardEl = card as HTMLElement
      if (isDarkMode) {
        cardEl.style.backgroundColor = '#222222'
        cardEl.style.borderLeftColor = '#654D43'
      } else {
        cardEl.style.backgroundColor = '#FAF2EF'
        cardEl.style.borderLeftColor = '#CB7C5D'
      }
      cardEl.style.borderLeftWidth = '4px'
      cardEl.style.borderLeftStyle = 'solid'
    })
  }

  // 动态更新OG图片meta标签
  const updateSeoMetaWithOgImage = (ogImageUrl: string) => {
    if (!pkData.value) return

    const user1 = pkData.value.user1.login
    const user2 = pkData.value.user2.login
    const currentDomain = getSiteUrl()
    const pageUrl = `${currentDomain}/github_compare?user1=${user1}&user2=${user2}`

    useSeoMeta({
      ogImage: ogImageUrl,
      twitterImage: ogImageUrl,
      ogUrl: pageUrl,
    })

    useHead({
      meta: [
        { property: 'og:image', content: ogImageUrl, key: 'og:image' },
        { name: 'twitter:image', content: ogImageUrl, key: 'twitter:image' },
        { property: 'og:url', content: pageUrl, key: 'og:url' }
      ]
    })

    if (import.meta.client) {
      nextTick(() => {
        let ogImageMeta = document.querySelector('meta[property="og:image"]') as HTMLMetaElement
        if (!ogImageMeta) {
          ogImageMeta = document.createElement('meta')
          ogImageMeta.setAttribute('property', 'og:image')
          document.head.appendChild(ogImageMeta)
        }
        ogImageMeta.setAttribute('content', ogImageUrl)

        let twitterImageMeta = document.querySelector('meta[name="twitter:image"]') as HTMLMetaElement
        if (!twitterImageMeta) {
          twitterImageMeta = document.createElement('meta')
          twitterImageMeta.setAttribute('name', 'twitter:image')
          document.head.appendChild(twitterImageMeta)
        }
        twitterImageMeta.setAttribute('content', ogImageUrl)

        console.log('已强制更新比较页面OG图片meta标签:', ogImageUrl)
      })
    }
  }

  // 检查是否已有比较页面OG图片
  const checkExistingCompareOgImage = async (user1: string, user2: string) => {
    try {
      const response = await $fetch(`/api/github/compare-og-image/${user1}-vs-${user2}`)
      if (response.success && response.ogImageUrl) {
        ogImageUrl.value = response.ogImageUrl
        ogImageGenerated.value = true
        console.log('找到已有的比较页面OG图片:', response.ogImageUrl)

        if (pkData.value) {
          updateSeoMetaWithOgImage(response.ogImageUrl)
        }
      }
    } catch (error) {
      console.log('未找到已有的比较页面OG图片，将在分析完成后生成')
    }
  }

  // Remove fetchReportData function since we handle direct HTTP responses now
  // This was only needed for SSE-based endpoints that return JSON URLs

  // 监听路由变化 - 只在客户端挂载后执行
  watch(() => route.query, async (newQuery) => {
    if (isMounted.value && newQuery.user1 && newQuery.user2) {
      // 重置加载状态
      loading.value = true
      isLoadingJson.value = false
      pkData.value = null
      ogImageGenerated.value = false
      ogImageUrl.value = ''

      // 检查是否已有OG图片
      checkExistingCompareOgImage(newQuery.user1 as string, newQuery.user2 as string)

      // 使用可复用函数获取数据
      await fetchComparisonData(newQuery.user1 as string, newQuery.user2 as string)
    }
  })

  // 监听路由变化，重置状态 - 只在客户端执行
  watch(
    route,
    () => {
      if (isMounted.value && (!route.query.user1 || !route.query.user2)) {
        pkData.value = null
        loading.value = false
        isLoadingJson.value = false
      }
    },
    { immediate: false } // 改为 false 避免 SSR 时立即执行
  )

  onUnmounted(() => {
    if (progressInterval) {
      clearInterval(progressInterval)
    }
    // 清理模拟加载文本
    if (currentSimulationCleanup) {
      currentSimulationCleanup()
      currentSimulationCleanup = null
    }
  })
</script>

<style scoped>
  .compare-page-bg {
    background-image: url('~/assets/image/bgimg.png');
  }

  .dark .compare-page-bg {
    background-image: url('~/assets/image/bgimgdark_1.png');
  }

  .custom-input {
    transition: all 0.3s ease;
  }

  /* 自定义脉冲动画，与学者比较页面保持一致 */
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }

  .custom-input:focus-within {
    box-shadow: 0 0 0 3px rgba(203, 124, 93, 0.1);
  }



  .text-primary-100 {
    color: #CB7C5D;
  }

  .text-primary-200 {
    color: #B86B4F;
  }

  .bg-black-100 {
    background-color: #000000;
  }

  .sty_f_r_end {
    display: flex;
    justify-content: flex-end;
  }

  .f-cer {
    display: flex;
    align-items: center;
  }

  .fx-cer {
    display: flex;
    align-items: center;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .grid-cols-2 {
      grid-template-columns: 1fr;
    }

    .gap-7\.5 {
      gap: 1rem;
    }

    .px-30 {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .w-120 {
      width: 100%;
    }

    .w-\[280px\] {
      width: 100%;
    }
  }
</style>
