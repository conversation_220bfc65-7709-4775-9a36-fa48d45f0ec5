<template>
  <div class="px-30 h-full" style="margin-top: 4rem">
    <!-- 激活码弹窗 -->
    <InviteCodeModal
      v-if="showInviteModal"
      :error="inviteError"
      :loading="inviteLoading"
      @close="showInviteModal = false"
      @submit="handleSubmitActivationCode"
      @waiting-list="onShowWaitingListModal"
    />
    <WaitingListModal
      v-if="showWaitingListModal"
      @close="showWaitingListModal = false"
      @back="onBackToInviteCode"
    />
    <div
      v-if="inviteSuccess"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black/40"
    >
      <div
        class="bg-white rounded-2xl shadow-xl p-8 pt-7 pb-7 w-full max-w-96 relative text-center"
      >
        <div class="text-lg font-semibold text-black mb-6">
          Enter Invite Code
          <button
            class="absolute top-6 right-6 text-gray-400 hover:text-gray-600 transition-colors text-xl"
            @click="inviteSuccess = false"
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 5L5 15M5 5L15 15"
                stroke="#BDBDBD"
                stroke-width="2"
                stroke-linecap="round"
              />
            </svg>
          </button>
        </div>
        <div class="flex justify-center mb-4">
          <div class="bg-green-500 rounded-full w-12 h-12 flex items-center justify-center mx-auto">
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
              <circle cx="16" cy="16" r="16" fill="none" />
              <path
                d="M10 17.5L14 21.5L22 13.5"
                stroke="#fff"
                stroke-width="2.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>
        <div class="text-xl font-bold text-black mb-2">Verification Successful</div>
        <div class="text-base text-gray-700 mb-7">
          Congratulations! Your invitation code has been verified successfully
        </div>
        <button
          class="w-full h-12 bg-black text-white rounded-lg font-semibold text-base transition hover:bg-gray-900"
          @click="goHome"
        >
          Ok
        </button>
      </div>
    </div>

    <template v-if="loading || isLoadingJson">
      <!-- 骨架屏组件 -->
      <div class="min-h-[60vh]">
        <!-- PK卡片骨架屏 -->
        <div class="relative">
          <div class="grid grid-cols-2 gap-7.5 mb-7.5">
            <!-- 左侧研究者骨架屏 -->
            <div class="bg-[#9BA3C1]/20 dark:bg-gray-600/20 rounded-2xl p-7.5 relative animate-pulse">
              <div class="absolute -top-5 left-7.5">
                <div class="w-15 h-15 rounded-full bg-gray-200/30 dark:bg-gray-600/30 border-4 border-white dark:border-gray-800"></div>
              </div>
              <div class="mt-7.5">
                <div class="h-8 bg-gray-200/30 dark:bg-gray-600/30 rounded w-3/4 mb-3"></div>
                <div class="h-5 bg-gray-200/25 dark:bg-gray-600/25 rounded w-1/2 mb-4"></div>
                <div class="flex flex-wrap gap-2 mb-4">
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-20"></div>
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-24"></div>
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-16"></div>
                </div>
                <div class="space-y-2">
                  <div class="h-6 bg-gray-200/25 dark:bg-gray-600/25 rounded w-5/6"></div>
                  <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-4/6"></div>
                </div>
              </div>
            </div>

            <!-- 右侧研究者骨架屏 -->
            <div class="bg-[#C6A69B]/20 dark:bg-gray-600/20 rounded-2xl p-7.5 relative animate-pulse">
              <div class="absolute -top-5 left-7.5">
                <div class="w-15 h-15 rounded-full bg-gray-200/30 dark:bg-gray-600/30 border-4 border-white dark:border-gray-800"></div>
              </div>
              <div class="mt-7.5">
                <div class="h-8 bg-gray-200/30 dark:bg-gray-600/30 rounded w-3/4 mb-3"></div>
                <div class="h-5 bg-gray-200/25 dark:bg-gray-600/25 rounded w-1/2 mb-4"></div>
                <div class="flex flex-wrap gap-2 mb-4">
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-20"></div>
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-24"></div>
                  <div class="h-7 bg-gray-200/20 dark:bg-gray-600/20 rounded-full w-16"></div>
                </div>
                <div class="space-y-2">
                  <div class="h-6 bg-gray-200/25 dark:bg-gray-600/25 rounded w-5/6"></div>
                  <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-4/6"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- VS标志骨架屏 -->
          <div class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 z-10">
            <div class="w-20 h-20 rounded-full bg-gray-200/40 dark:bg-gray-600/40 animate-pulse"></div>
          </div>
        </div>
        <Loading :visible="loading" :data="thinking" @update:visible="router.replace('/analysis')" />

        <!-- 雷达图骨架屏 -->
        <div class="bg-white dark:bg-[#1a1a1b] rounded-2xl p-7.5 mb-7.5">
          <div class="animate-pulse">
            <div class="h-8 bg-gray-100/60 dark:bg-gray-600/60 rounded w-1/3 mx-auto mb-8"></div>
            <div class="h-[300px] bg-gray-100/40 dark:bg-gray-600/40 rounded-lg"></div>
          </div>
        </div>

        <!-- 指标对比骨架屏 -->
        <div class="bg-white dark:bg-[#1a1a1b] rounded-2xl p-7.5 mb-7.5">
          <div class="animate-pulse">
            <div class="h-8 bg-gray-100/60 dark:bg-gray-600/60 rounded w-1/4 mx-auto mb-8"></div>
            <div class="grid grid-cols-2 gap-7.5">
              <div class="space-y-4">
                <div class="h-6 bg-gray-100/50 dark:bg-gray-600/50 rounded w-full"></div>
                <div class="h-6 bg-gray-100/50 dark:bg-gray-600/50 rounded w-5/6"></div>
                <div class="h-6 bg-gray-100/50 dark:bg-gray-600/50 rounded w-4/6"></div>
              </div>
              <div class="space-y-4">
                <div class="h-6 bg-gray-100/50 dark:bg-gray-600/50 rounded w-full"></div>
                <div class="h-6 bg-gray-100/50 dark:bg-gray-600/50 rounded w-5/6"></div>
                <div class="h-6 bg-gray-100/50 dark:bg-gray-600/50 rounded w-4/6"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Roast骨架屏 -->
        <div class="bg-[#FDF7F7] dark:bg-[#222222] rounded-2xl p-7.5 mt-7.5">
          <div class="animate-pulse">
            <div class="h-10 bg-gray-200/20 dark:bg-gray-600/20 rounded w-1/4 mx-auto mb-6"></div>
            <div class="space-y-4 max-w-[800px] mx-auto">
              <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-full"></div>
              <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-5/6"></div>
              <div class="h-4 bg-gray-200/20 dark:bg-gray-600/20 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-else-if="pkData">
      <div class="relative bg-cover bg-center bg-no-repeat rounded-xl compare-page-bg">
        <!-- PK卡片部分 -->
        <div class="grid grid-cols-2 gap-7.5 mb-7.5">
          <!-- 左侧研究者 -->
          <div class="rounded-xl p-5 relative flex flex-col items-center text-center min-h-[320px]">
            <div class="absolute -top-5 left-1/2 -translate-x-1/2">
              <div class="flex items-center gap-2">
                <img
                  :src="pkData.researcher1.avatar"
                  class="w-15 h-15 rounded-full border-4 border-white object-cover"
                />
              </div>
            </div>
            <div class="mt-7.5 w-full flex flex-col justify-between flex-1">
              <div class="flex flex-col">
                <div class="text-white text-xl font-bold">{{ pkData.researcher1.name }}</div>
                <div class="text-white dark:text-[#C6C6C6] mt-1.5 flex items-start justify-center text-sm max-w-[400px] mx-auto">
                  <SvgIcon name="verified" class="text-4 mr-1.5 mt-0.5 flex-shrink-0" />
                  <span class="text-center line-clamp-2">{{ pkData.researcher1.affiliation }}</span>
                </div>
              </div>
              <div class="mt-auto">
                <div class="flex flex-wrap gap-1.5 mb-3 justify-center items-center min-h-[32px]">
                  <div
                    v-for="(field, index) in pkData.researcher1.research_fields.slice(0, 3)"
                    :key="index"
                    class="px-2.5 py-1 bg-[#A1AED2] dark:bg-[#3C4356] rounded-md text-[#262D3F] dark:text-[#FAF9F5] text-xs whitespace-nowrap"
                  >
                    {{ field }}
                  </div>
                </div>
                <div class="text-white dark:text-[#C6C6C6] text-sm">
                  Wanna compare
                  <span class="text-[#364A83] dark:text-[#FDB852] font-bold">{{
                    pkData.researcher1.name
                  }}</span>
                  with other AI researchers?
                </div>
                <div class="text-white dark:text-[#C6C6C6] text-xs mt-1">
                  Just input their name, or Google Scholar link
                </div>
                <motion.div
                  :initial="{ opacity: 0, y: 10 }"
                  :animate="{ opacity: 1, y: 0 }"
                  :transition="{ duration: 0.3, ease: 'easeInOut', delay: 0.2 }"
                  class="f-cer mt-5 mb-4"
                >
                  <div
                    class="custom-input border rounded-full bg-white border-black min-h-14 w-120 p-1 flex items-center justify-between gap-3 pl-6"
                  >
                    <SearchInput ref="searchInputRef1" placeholder="Researcher name" @enter-search="handleLeftCompare" />
                    <ActionButton :imgSrc="stars" buttonText="Compare" @click="handleLeftCompare" />
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
          <!-- 右侧研究者 -->
          <div class="rounded-xl p-5 relative flex flex-col items-center text-center min-h-[320px]">
            <div class="absolute -top-5 left-1/2 -translate-x-1/2">
              <div class="flex items-center gap-2">
                <img
                  :src="pkData.researcher2.avatar"
                  class="w-15 h-15 rounded-full border-4 border-white object-cover"
                />
              </div>
            </div>
            <div class="mt-7.5 w-full flex flex-col justify-between flex-1">
              <div class="flex flex-col">
                <div class="text-white text-xl font-bold">{{ pkData.researcher2.name }}</div>
                <div class="text-white dark:text-[#C6C6C6] mt-1.5 flex items-start justify-center text-sm max-w-[400px] mx-auto">
                  <SvgIcon name="verified-brown" class="text-4 mr-1.5 mt-0.5 flex-shrink-0 text-red-500" />
                  <span class="text-center line-clamp-2">{{ pkData.researcher2.affiliation }}</span>
                </div>
              </div>
              <div class="mt-auto">
                <div class="flex flex-wrap gap-1.5 mb-3 justify-center items-center min-h-[32px]">
                  <div
                    v-for="(field, index) in pkData.researcher2.research_fields.slice(0, 3)"
                    :key="index"
                    class="px-2.5 py-1 bg-[#E7CDC3] dark:bg-[#413834] rounded-md text-[#7F4832] dark:text-[#FAF9F5] text-xs whitespace-nowrap"
                  >
                    {{ field }}
                  </div>
                </div>
                <div class="text-white dark:text-[#C6C6C6] text-sm">
                  Wanna compare
                  <span class="text-[#6D4130] dark:text-[#5765F2] font-bold">{{
                    pkData.researcher2.name
                  }}</span>
                  with other AI researchers?
                </div>
                <div class="text-white dark:text-[#C6C6C6] text-xs mt-1">
                  Just input their name, or Google Scholar link
                </div>
                <motion.div
                  :initial="{ opacity: 0, y: 10 }"
                  :animate="{ opacity: 1, y: 0 }"
                  :transition="{ duration: 0.3, ease: 'easeInOut', delay: 0.2 }"
                  class="f-cer mt-5 mb-4"
                >
                  <div
                    class="custom-input border rounded-full bg-white border-black min-h-14 w-120 p-1 flex items-center justify-between gap-3 pl-6"
                  >
                    <SearchInput ref="searchInputRef2" placeholder="Researcher name" @enter-search="handleRightCompare" />
                    <ActionButton :imgSrc="stars" buttonText="Compare" @click="handleRightCompare" />
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 雷达图部分 -->
      <div class="rounded-2xl p-7.5 mb-7.5">
        <RadarChart :researcher1="pkData.researcher1" :researcher2="pkData.researcher2" size="large" />
      </div>

      <div class="flex items-center justify-center mb-10 flex-col gap-4">
        <div
          class="h-13.5 w-[210px] text-4 font-600 text-white bg-[#CB7C5D] dark:bg-[#654D43] dark:border dark:border-[#866457] fx-cer justify-center rounded-full gap-2 cursor-pointer"
          @click="showCompareCard = true"
          >
          <div class="i-proicons:x-twitter wh-5 font-600" data-v-2dc31878=""></div>
          Share
        </div>
      </div>

      <VSCard
        :show="showPopup"
        :user="user"
        :income="220000"
        :is-dark="isDark"
        :stats="stats"
        :role-model="roleModel"
        :conferenceDistribution="initData.dataBlocks.publicationInsight.conferenceDistribution"
        @close="showPopup = false"
        :insights-items="items"
        :pk-data="pkData"
        />

      <ShareCardCompare
        :show="showCompareCard"
        :is-dark="isDark"
        :researcher1="transformResearcherData(pkData?.researcher1)"
        :researcher2="transformResearcherData(pkData?.researcher2)"
        @close="showCompareCard = false"
      />

      <!-- 隐藏的分享卡片用于 OG 图片生成 -->
      <div
        v-if="pkData && pkData.researcher1 && pkData.researcher2 && transformResearcherData(pkData.researcher1) && transformResearcherData(pkData.researcher2)"
        :style="{
          position: 'fixed',
          top: '0',
          left: '0',
          transform: 'translateX(-100%)',
          zIndex: '-1',
          pointerEvents: 'none'
        }"
      >
        <ShareCardCompare
          :show="true"
          :is-dark="isDark"
          :researcher1="transformResearcherData(pkData.researcher1)"
          :researcher2="transformResearcherData(pkData.researcher2)"
          @close="() => {}"
        />
      </div>

      <!-- 指标对比和代表作部分 -->
      <CompareMetrics :researcher1="pkData.researcher1" :researcher2="pkData.researcher2" />

      <!-- Roast 部分 -->
      <div 
        class="rounded-2xl p-7.5 mt-10 bg-cover bg-center bg-no-repeat border dark:border-[#27282D]" 
        :style="{ 
          backgroundImage: isDark 
            ? 'url(/image/compare-roast-dark.png)' 
            : 'url(/image/compare-roast.png)',
          backgroundSize: 'cover',
          minHeight: '200px'
        }"
      >
        <div class="text-center flex flex-col">
          <i class="text-[56px] font-extrabold text-[#CB7C5D] clash-display mb-5">Roast</i>
          <div 
            class="text-center max-w-[800px] mx-auto text-18px leading-8"
            :class="isDark ? 'text-white' : 'text-[#555658]'"
          >
            {{ pkData.roast }}
          </div>
        </div>
      </div>

      <!-- 比较部分 -->
      <div class="flex flex-col items-center mt-20">
        <div class="text-[56px] clash-semibold font-semibold text-center max-w-[800px] leading-[72.8px]">
          Or compare the profiles<br />
          of two AI researchers
        </div>
        <div class="flex items-center gap-4 mt-10 w-full max-w-[800px]">
          <div class="flex-1 sty_f_r_end">
            <input
              v-model="researcher1Input"
              type="text"
              placeholder="Researcher name"
              class="custom-input w-[280px] h-13.5 px-5 rounded-full border border-black bg-white font-bold placeholder-#7C8493 placeholder:font-normal text-black placeholder-gray-300 min-w-0"
              style="outline: none !important; box-shadow: none !important; border-color: black !important;"
              @keyup.enter="handleCompare"
            />
          </div>
          <div class="text-3xl font-bold italic" style="color: #CB7C5D;">VS</div>
          <div class="flex-1">
            <input
              v-model="researcher2Input"
              type="text"
              placeholder="Researcher name"
              class="custom-input w-[280px] h-13.5 px-5 rounded-full border border-black bg-white font-bold placeholder-#7C8493 placeholder:font-normal text-black placeholder-gray-300 min-w-0"
              style="outline: none !important; box-shadow: none !important; border-color: black !important;"
              @keyup.enter="handleCompare"
            />
          </div>
        </div>
        <button
          @click="handleCompare"
          class="mt-7.5 flex items-center justify-center gap-2 rounded-full bg-black-100 text-white hover:bg-black-100/90 transition-colors w-[210px] h-[54px] dark:border-3 dark:border-white"
        >
          <img src="/image/stars.png" alt="compare" />
          <span class="text-base font-bold">Compare</span>
        </button>
        <div class="text-sm text-gray-500 mt-7.5 mb-20">
          By clicking Compare you agree to our
          <a href="/terms" class="text-primary-100 hover:text-primary-200 underline"
            >terms of service</a
          >
        </div>
      </div>
    </template>
    <template v-else>
      <!-- 兜底展示 -->
      <div class="min-h-[60vh] flex flex-col items-center justify-center">
        <div class="w-20 h-20 rounded-full bg-[#FDF7F7] flex items-center justify-center mb-6">
          <div class="i-carbon:warning-alt text-primary-100 text-3xl"></div>
        </div>
        <div class="text-[32px] font-bold clash-display text-center mb-4">No Data Available</div>
        <div class="text-gray-600 text-center max-w-[500px] mb-10">
          Sorry, we couldn't find the comparison data. Please try comparing other researchers.
        </div>
        <!-- 比较部分 -->
        <div class="w-full max-w-[800px]">
          <div class="text-[42px] font-bold clash-display text-center leading-[1.2] mb-10">
            Compare the profiles<br />
            of two AI researchers
          </div>
          <div class="flex items-center gap-4">
            <div class="flex-1">
              <input
                v-model="researcher1Input"
                type="text"
                placeholder="Researcher name"
                class="w-full h-12 px-5 rounded-full border border-black bg-white font-bold placeholder-#7C8493 placeholder:font-normal text-black placeholder-gray-300"
                style="outline: none !important; box-shadow: none !important; border-color: black !important;"
                @keyup.enter="handleCompare"
              />
            </div>
            <div class="text-3xl font-bold italic" style="color: #CB7C5D;">VS</div>
            <div class="flex-1">
              <input
                v-model="researcher2Input"
                type="text"
                placeholder="Researcher name"
                class="w-full h-12 px-5 rounded-full border border-black bg-white font-bold placeholder-#7C8493 placeholder:font-normal text-black placeholder-gray-300"
                style="outline: none !important; box-shadow: none !important; border-color: black !important;"
                @keyup.enter="handleCompare"
              />
            </div>
          </div>
          <div class="flex justify-center mt-7.5">
            <button
              @click="handleCompare"
              class="flex items-center justify-center gap-2 rounded-full bg-black-100 text-white hover:bg-black-100/90 transition-colors w-[210px] h-[54px] dark:border-3 dark:border-white"
            >
              <img src="/image/stars.png" alt="compare" />
              <span class="text-base font-bold">Compare</span>
            </button>
          </div>
          <div class="text-sm text-gray-500 text-center mt-7.5 mb-20">
            By clicking Compare you agree to our
            <a href="/terms" class="text-primary-100 hover:text-primary-200 underline"
              >terms of service</a
            >
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
  import stars from '@/assets/image/stars.png'
  import { getSiteUrl } from '~/utils/request'
  import { useRoute, useRouter } from 'vue-router'
  import { useEventStream } from '@/composables/useEventStream'
  import { motion } from "motion-v"
  import RadarChart from '@/components/RadarChart.vue'
  import CompareMetrics from '@/components/CompareMetrics.vue'
  import ShareCardCompare from '@/components/ShareCardCompare/index.vue'
  import { ref, onMounted, watch, onUnmounted, nextTick } from 'vue'
  import InviteCodeModal from '@/components/InviteCodeModal.vue'
  import WaitingListModal from '@/components/WaitingListModal.vue'
  import { submitActivationCode } from '~/api'
  import html2canvas from 'html2canvas-pro'
  import { uploadFileToS3, extractScholarId } from '~/utils'

  definePageMeta({
    middleware: 'auth',
  })

  // 动态 SEO 元数据
  const updateScholarCompareSeoMeta = (data: any) => {
    const researcher1Name = data.researcher1?.name || 'Researcher 1'
    const researcher2Name = data.researcher2?.name || 'Researcher 2'

    const title = `${researcher1Name} vs ${researcher2Name} - Scholar Comparison | DINQ`
    const description = `Compare researchers ${researcher1Name} and ${researcher2Name}. Analyze their academic achievements, publications, citations, and research impact.`

    // 获取研究领域
    const researcher1Fields = data.researcher1?.research_interests || []
    const researcher2Fields = data.researcher2?.research_interests || []
    const allFields = [...new Set([...researcher1Fields, ...researcher2Fields])]

    const keywords = [
      researcher1Name,
      researcher2Name,
      'Scholar Comparison',
      'Researcher Comparison',
      'Academic Analysis',
      'Research Impact',
      'Citation Analysis',
      ...allFields
    ].join(', ')

    // 获取当前域名
    const currentDomain = getSiteUrl()
    const pageUrl = `${currentDomain}/compare?researcher1=${encodeURIComponent(researcher1Name)}&researcher2=${encodeURIComponent(researcher2Name)}`

    // 使用可预测的OG图片URL或已生成的URL
    const currentOgImageUrl = ogImageUrl.value || getPredictableScholarCompareOgImageUrl(data.researcher1, data.researcher2)

    useSeoMeta({
      title,
      description,
      keywords,

      // Open Graph
      ogTitle: title,
      ogDescription: description,
      ogType: 'website',
      ogUrl: pageUrl,
      ogImage: currentOgImageUrl,

      // Twitter Card
      twitterCard: 'summary_large_image',
      twitterTitle: title,
      twitterDescription: description,
      twitterImage: currentOgImageUrl,

      // 额外的 meta 标签
      author: 'DINQ',
      'article:tag': allFields.join(', '),
    })

    // 设置页面标题
    useHead({
      title,
      meta: [
        {
          name: 'robots',
          content: 'index, follow'
        },
        {
          name: 'theme-color',
          content: '#CB7C5D'
        }
      ],
      link: [
        {
          rel: 'canonical',
          href: pageUrl
        }
      ]
    })
  }

  const route = useRoute()
  const router = useRouter()
  const { currentUser } = useFirebaseAuth()

  const { thinking, loading, reportDataInfo, connectWithObj, limitInfo, pkData } = useEventStream()

  const researcher1Input = ref('')
  const researcher2Input = ref('')
  // const pkData = ref<any>(null) // 使用 useEventStream 中的 pkData
  
  // 为顶部搜索框添加独立的ref
  const searchInputRef1 = ref()
  const searchInputRef2 = ref()

  const isAnalyzing = ref(false)
  const analysisProgress = ref(0)
  let progressInterval: NodeJS.Timeout | null = null

  const isLoadingJson = ref(false)

  const showInviteModal = ref(false)
  const inviteError = ref('')
  const inviteLoading = ref(false)
  const inviteSuccess = ref(false)
  const showWaitingListModal = ref(false)
  const showPopup = ref(false)
  const showCompareCard = ref(false)

  // OG 图片相关状态
  const ogImageUrl = ref('')
  const ogImageGenerated = ref(false)

  const isDark = ref(false)
  onMounted(() => {
    isDark.value = document.documentElement.classList.contains('dark')
    const observer = new MutationObserver(() => {
      isDark.value = document.documentElement.classList.contains('dark')
    })
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })
  })

  // Scholar 比较页面工具函数
  const generateScholarCompareOgImageFileName = (researcher1: any, researcher2: any): string => {
    const scholar1Id = researcher1?.scholarId || researcher1?.scholar_id
    const scholar2Id = researcher2?.scholarId || researcher2?.scholar_id

    if (scholar1Id && scholar2Id) {
      // 使用Scholar ID（推荐方案）
      return `scholar-compare-${scholar1Id}-vs-${scholar2Id}-latest.png`
    } else {
      // 备选方案，使用姓名
      const name1 = sanitizeName(researcher1?.name || 'researcher1')
      const name2 = sanitizeName(researcher2?.name || 'researcher2')
      return `scholar-compare-${name1}-vs-${name2}-latest.png`
    }
  }

  // 姓名清理函数
  const sanitizeName = (name: string): string => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')  // 替换非字母数字为连字符
      .replace(/-+/g, '-')         // 合并多个连字符
      .replace(/^-|-$/g, '')       // 移除首尾连字符
  }

  // 生成可预测的Scholar比较页面OG图片URL
  const getPredictableScholarCompareOgImageUrl = (researcher1: any, researcher2: any): string => {
    const fileName = generateScholarCompareOgImageFileName(researcher1, researcher2)
    return `https://dinq-share-og.s3.us-east-2.amazonaws.com/shares/${fileName}`
  }

  // 转换研究者数据格式以匹配ShareCardCompare组件的接口
  const transformResearcherData = (researcher: any) => {
    if (!researcher) return null

    return {
      name: researcher.name || 'Unknown Researcher',
      affiliation: researcher.affiliation || 'Unknown Affiliation',
      avatar: researcher.avatar || '/image/avator.png',
      research_fields: researcher.research_interests || researcher.research_fields || [],
      total_citations: researcher.total_citations || 0,
      top_tier_papers: researcher.top_tier_papers || 0,
      first_author_papers: researcher.first_author_papers || 0,
      first_author_citations: researcher.first_author_citations || 0,
      most_cited_paper: researcher.most_cited_paper || { citations: 0 }
    }
  }

  // Scholar比较页面图片处理逻辑（参考GitHub比较页面的完整实现）
  const handleScholarCompareImageProcessing = (clonedDoc: Document, isDarkMode: boolean) => {
    const clonedElement = clonedDoc.querySelector('[data-card-id="share-card-compare"]')
    if (!clonedElement) return

    // 1. 替换操作按钮为版权信息和二维码
    const buttonContainer = clonedElement.querySelector('[data-action-buttons]')
    if (buttonContainer) {
      const copyrightDiv = clonedDoc.createElement('div')
      copyrightDiv.style.cssText = 'font-size: 12px; color: #666; font-weight: 400; white-space: nowrap;'
      copyrightDiv.textContent = 'Copyright @ 2025 DINQ Inc. All rights reserved'

      const qrCode = clonedDoc.createElement('img')
      qrCode.src = '/image/qrcode.png'
      qrCode.alt = 'QR Code'
      qrCode.style.cssText = 'width: 48px; height: 48px; flex-shrink: 0; margin-left: 8px;'

      const bottomRightContainer = clonedDoc.createElement('div')
      bottomRightContainer.style.cssText = 'position: absolute; bottom: 16px; right: 32px; display: flex; align-items: center; gap: 8px; z-index: 10;'
      bottomRightContainer.appendChild(copyrightDiv)
      bottomRightContainer.appendChild(qrCode)

      buttonContainer.parentNode?.replaceChild(bottomRightContainer, buttonContainer)
    }

    // 2. 替换SVG图标为PNG图片
    const svgIconElements = clonedElement.querySelectorAll('svg.svg-icon')
    svgIconElements.forEach((svgEl) => {
      const svgElement = svgEl as SVGElement
      const useElement = svgElement.querySelector('use')
      if (!useElement) return

      const iconId = useElement.getAttribute('xlink:href') || useElement.getAttribute('href')
      if (!iconId) return

      const imgElement = clonedDoc.createElement('img')

      // Scholar比较页面图标映射表
      const iconMappings: Record<string, { src: string; alt: string; className: string }> = {
        '#icon-verified': { src: '/image/share-ver.png', alt: 'verified', className: 'w-4 h-4' },
        '#icon-research': { src: '/image/share-role.png', alt: 'research', className: 'w-4 h-4' },
        '#icon-papers': { src: '/image/sharecard/papers.png', alt: 'papers', className: 'w-4 h-4' },
        '#icon-citations': { src: '/image/sharecard/citations.png', alt: 'citations', className: 'w-4 h-4' },
        '#icon-hindex': { src: '/image/sharecard/hindex.png', alt: 'h-index', className: 'w-4 h-4' }
      }

      const mapping = iconMappings[iconId]
      if (mapping) {
        imgElement.src = mapping.src
        imgElement.alt = mapping.alt
        imgElement.className = mapping.className
        svgElement.parentNode?.replaceChild(imgElement, svgElement)
      }
    })

    // 3. 修复头像路径
    const avatarImages = clonedElement.querySelectorAll('img[src*="@/assets/image/avator.png"]')
    avatarImages.forEach(img => {
      const imgEl = img as HTMLImageElement
      imgEl.src = '/image/avator.png'
    })

    // 4. 处理雷达图Canvas（关键：Chart.js Canvas处理）
    fixScholarRadarChartCanvas(clonedElement)

    // 5. 修复标签样式（关键：Scholar比较页面特有的标签处理）
    fixScholarCompareTagStyles(clonedElement, isDarkMode)

    // 6. 修复Scholar比较页面特有的样式
    fixScholarCompareSpecificStyles(clonedElement, isDarkMode)

    // 7. 修复通用样式
    fixScholarCompareTextColors(clonedElement, isDarkMode)
    fixScholarCompareGlassEffects(clonedElement, isDarkMode)
  }

  // 修复Scholar雷达图Canvas（关键：Chart.js Canvas处理）
  const fixScholarRadarChartCanvas = (clonedElement: Element) => {
    // 查找雷达图容器
    const radarCharts = clonedElement.querySelectorAll('.radar-chart, [class*="radar"], canvas')

    radarCharts.forEach(radarChart => {
      let canvas: HTMLCanvasElement | null = null

      // 如果当前元素就是canvas
      if (radarChart.tagName === 'CANVAS') {
        canvas = radarChart as HTMLCanvasElement
      } else {
        // 否则在容器内查找canvas
        canvas = radarChart.querySelector('canvas')
      }

      if (canvas) {
        try {
          // 获取原始Canvas的数据URL
          const dataURL = canvas.toDataURL('image/png')

          // 创建一个img元素替换canvas
          const img = document.createElement('img')
          img.src = dataURL
          img.style.width = canvas.style.width || `${canvas.width}px`
          img.style.height = canvas.style.height || `${canvas.height}px`
          img.style.maxWidth = '100%'
          img.style.maxHeight = '100%'
          img.style.display = 'block'

          // 替换canvas为img
          canvas.parentNode?.replaceChild(img, canvas)

          console.log('Scholar雷达图Canvas已转换为图片')
        } catch (error) {
          console.error('转换Scholar雷达图Canvas失败:', error)

          // 如果转换失败，尝试设置Canvas的样式以确保html2canvas能正确处理
          canvas.style.backgroundColor = 'transparent'
          canvas.style.display = 'block'
        }
      }
    })
  }

  // 修复Scholar比较页面标签样式（关键功能）
  const fixScholarCompareTagStyles = (clonedElement: Element, isDarkMode: boolean) => {
    const allTags = clonedElement.querySelectorAll('.tag-component, [class*="tag"]')

    allTags.forEach((tag) => {
      const tagEl = tag as HTMLElement

      // 检查标签的父容器来判断是左侧还是右侧
      let isLeftSide = false
      let isRightSide = false

      // 向上查找父元素，寻找justify-end（左侧）或justify-start（右侧）
      let parent = tagEl.parentElement
      while (parent && parent !== clonedElement) {
        const parentClasses = parent.className

        if (parentClasses.includes('justify-end')) {
          isLeftSide = true
          break
        }
        if (parentClasses.includes('justify-start')) {
          isRightSide = true
          break
        }
        parent = parent.parentElement
      }

      // 如果没有找到明确的左右标识，通过位置判断
      if (!isLeftSide && !isRightSide) {
        const rect = tagEl.getBoundingClientRect()
        const containerRect = clonedElement.getBoundingClientRect()
        const centerX = containerRect.left + containerRect.width / 2

        if (rect.left < centerX) {
          isLeftSide = true
        } else {
          isRightSide = true
        }
      }

      if (isDarkMode) {
        if (isLeftSide) {
          // Scholar左侧标签深色模式样式
          tagEl.style.backgroundColor = '#3C4356'
          tagEl.style.borderColor = '#7F8EB7'
          tagEl.style.color = '#C2C5CE'
          tagEl.style.borderWidth = '0.5px'
          tagEl.style.borderStyle = 'solid'
        } else if (isRightSide) {
          // Scholar右侧标签深色模式样式
          tagEl.style.backgroundColor = '#413834'
          tagEl.style.borderColor = '#71635E'
          tagEl.style.color = '#E1BCAD'
          tagEl.style.borderWidth = '0.5px'
          tagEl.style.borderStyle = 'solid'
        }
      } else {
        if (isLeftSide) {
          // Scholar左侧标签亮色模式样式
          tagEl.style.backgroundColor = '#EEF1F9'
          tagEl.style.borderColor = '#7F8EB7'
          tagEl.style.color = '#7F8EB7'
          tagEl.style.borderWidth = '0.5px'
          tagEl.style.borderStyle = 'solid'
        } else if (isRightSide) {
          // Scholar右侧标签亮色模式样式
          tagEl.style.backgroundColor = '#FBEAE3'
          tagEl.style.borderColor = '#CB7C5D'
          tagEl.style.color = '#CB7C5D'
          tagEl.style.borderWidth = '0.5px'
          tagEl.style.borderStyle = 'solid'
        }
      }

      // 确保标签的基本样式
      tagEl.style.borderRadius = '4px'
      tagEl.style.padding = '4px 8px'
      tagEl.style.fontSize = '12px'
      tagEl.style.fontWeight = '400'
      tagEl.style.display = 'inline-block'
    })
  }

  // Scholar比较页面特有样式修复
  const fixScholarCompareSpecificStyles = (clonedElement: Element, isDarkMode: boolean) => {
    // 修复VS分隔符样式
    const vsSeparators = clonedElement.querySelectorAll('.vs-separator, [class*="vs"]')
    vsSeparators.forEach(separator => {
      const sepEl = separator as HTMLElement
      if (isDarkMode) {
        sepEl.style.color = '#FAF9F5'
        sepEl.style.backgroundColor = '#27282D'
      } else {
        sepEl.style.color = '#1F2937'
        sepEl.style.backgroundColor = '#F9FAFB'
      }
    })

    // 修复对比卡片的边框
    const compareCards = clonedElement.querySelectorAll('.compare-card, [class*="compare"]')
    compareCards.forEach(card => {
      const cardEl = card as HTMLElement
      if (isDarkMode) {
        cardEl.style.borderColor = '#27282D'
      } else {
        cardEl.style.borderColor = '#E5E7EB'
      }
    })

    // 修复Scholar特有的统计卡片
    const statCards = clonedElement.querySelectorAll('[class*="stat"], [class*="metric"]')
    statCards.forEach(card => {
      const cardEl = card as HTMLElement
      if (isDarkMode) {
        cardEl.style.borderColor = '#27282D'
        cardEl.style.backgroundColor = 'rgba(20, 20, 21, 0.85)'
      } else {
        cardEl.style.borderColor = '#E5E7EB'
        cardEl.style.backgroundColor = 'rgba(255, 255, 255, 0.85)'
      }
    })
  }

  // 修复Scholar比较页面文字颜色
  const fixScholarCompareTextColors = (clonedElement: Element, isDarkMode: boolean) => {
    if (isDarkMode) {
      // 修复所有卡片的边框颜色
      const cardElements = clonedElement.querySelectorAll('.border-gray-200')
      cardElements.forEach(card => {
        (card as HTMLElement).style.borderColor = '#27282D'
      })

      // 修复主卡片的边框颜色
      const mainCard = clonedElement.querySelector('[data-card-id="share-card-compare"]')
      if (mainCard) {
        (mainCard as HTMLElement).style.borderColor = '#27282D'
      }

      // 修复卡片标题文字颜色
      const titleElements = clonedElement.querySelectorAll('.text-black')
      titleElements.forEach(title => {
        (title as HTMLElement).style.color = '#FAF9F5'
      })

      // 修复数值文字颜色
      const numberElements = clonedElement.querySelectorAll('[class*="text-gray"], [class*="text-slate"]')
      numberElements.forEach(element => {
        (element as HTMLElement).style.color = '#C6C6C6'
      })
    }

    // 确保主卡片背景渐变正确显示
    const mainCardDiv = clonedElement.querySelector('[data-card-id="share-card-compare"]')
    if (mainCardDiv) {
      const cardEl = mainCardDiv as HTMLElement
      if (isDarkMode) {
        cardEl.style.background = 'linear-gradient(to bottom, #141415, #141415)'
        cardEl.style.borderColor = '#27282D'
      } else {
        cardEl.style.background = 'linear-gradient(to bottom, #FFFFFF, #F4F2F1)'
        cardEl.style.borderColor = 'transparent'
      }
      cardEl.style.position = 'relative'
      cardEl.style.display = 'block'
    }
  }

  // 修复Scholar比较页面毛玻璃效果
  const fixScholarCompareGlassEffects = (clonedElement: Element, isDarkMode: boolean) => {
    const glassCards = clonedElement.querySelectorAll('[style*="backdrop-filter: blur(34px)"]')
    glassCards.forEach(card => {
      const cardEl = card as HTMLElement
      if (isDarkMode) {
        cardEl.style.backgroundColor = 'rgba(20, 20, 21, 0.85)'
        cardEl.style.backdropFilter = 'none'
        cardEl.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.3)'
      } else {
        cardEl.style.backgroundColor = 'rgba(255, 255, 255, 0.85)'
        cardEl.style.backdropFilter = 'none'
        cardEl.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.1)'
      }
    })

    // 确保所有文本元素在截图时可见
    const textElements = clonedElement.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div')
    textElements.forEach(element => {
      const el = element as HTMLElement
      if (el.style.color === 'transparent' || el.style.opacity === '0') {
        if (isDarkMode) {
          el.style.color = '#FAF9F5'
        } else {
          el.style.color = '#030229'
        }
        el.style.opacity = '1'
      }
    })
  }

  // 生成Scholar比较页面OG图片
  const generateScholarCompareOgImage = async (researcher1: any, researcher2: any) => {
    if (ogImageGenerated.value || !import.meta.client) return

    try {
      console.log('开始生成Scholar比较页面OG图片...', { researcher1: researcher1?.name, researcher2: researcher2?.name })

      // 等待Vue的响应式更新完成
      await nextTick()

      // 等待额外的渲染时间
      await new Promise(resolve => setTimeout(resolve, 500))

      // 调试：检查所有可能的分享卡片元素
      const allShareCards = document.querySelectorAll('[data-card-id]')
      console.log('页面中所有的data-card-id元素:', Array.from(allShareCards).map(el => el.getAttribute('data-card-id')))

      // 查找分享卡片元素
      const shareCardElement = document.querySelector('[data-card-id="share-card-compare"]') as HTMLElement
      if (!shareCardElement) {
        console.warn('未找到Scholar比较分享卡片元素，无法生成OG图片')
        console.log('当前pkData状态:', {
          hasPkData: !!pkData.value,
          hasResearcher1: !!pkData.value?.researcher1,
          hasResearcher2: !!pkData.value?.researcher2,
          ogImageGenerated: ogImageGenerated.value,
          researcher1Data: pkData.value?.researcher1,
          researcher2Data: pkData.value?.researcher2,
          transformedResearcher1: transformResearcherData(pkData.value?.researcher1),
          transformedResearcher2: transformResearcherData(pkData.value?.researcher2)
        })
        return
      }

      console.log('找到Scholar比较分享卡片元素，尺寸:', {
        width: shareCardElement.offsetWidth,
        height: shareCardElement.offsetHeight,
        scrollWidth: shareCardElement.scrollWidth,
        scrollHeight: shareCardElement.scrollHeight
      })

      // 等待图片资源加载完成
      const images = shareCardElement.getElementsByTagName('img')
      const imagePromises = [...images].map(img => {
        if (img.complete) return Promise.resolve()
        return new Promise(resolve => {
          img.onload = resolve
          img.onerror = resolve // 即使图片加载失败也继续
        })
      })
      await Promise.all(imagePromises)

      // 等待自定义字体加载完成
      if (document.fonts) {
        await document.fonts.ready
      }

      // 给浏览器额外的渲染时间
      await new Promise(resolve => setTimeout(resolve, 1000))

      console.log('所有资源加载完成，开始截图...')

      // 检测当前主题
      const isDarkMode = document.documentElement.classList.contains('dark')

      // 使用html2canvas截图，让图片保持动态尺寸
      const canvas = await html2canvas(shareCardElement, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: isDarkMode ? '#141415' : '#ffffff',
        logging: true,
        imageTimeout: 15000,
        foreignObjectRendering: false,
        scrollX: 0,
        scrollY: 0,
        onclone: (clonedDoc) => handleScholarCompareImageProcessing(clonedDoc, isDarkMode)
      })

      console.log('截图完成，canvas尺寸:', {
        width: canvas.width,
        height: canvas.height
      })

      // 转换为Blob
      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => {
          resolve(blob!)
        }, 'image/png', 1.0)
      })

      // 生成文件名
      const fileName = generateScholarCompareOgImageFileName(researcher1, researcher2)

      // 上传到S3
      const publicUrl = await uploadFileToS3(blob, 'image/png', fileName)

      console.log('Scholar比较页面OG图片上传成功:', publicUrl)

      // 保存URL到数据库
      await $fetch('/api/scholar/save-compare-og-image', {
        method: 'POST',
        body: {
          researcher1Id: researcher1?.scholarId || researcher1?.scholar_id,
          researcher2Id: researcher2?.scholarId || researcher2?.scholar_id,
          researcher1Name: researcher1?.name,
          researcher2Name: researcher2?.name,
          ogImageUrl: publicUrl
        }
      })

      // 更新本地状态
      ogImageUrl.value = publicUrl
      ogImageGenerated.value = true

      // 动态更新meta标签
      updateSeoMetaWithOgImage(publicUrl)

    } catch (error) {
      console.error('生成Scholar比较页面OG图片失败:', error)
    }
  }

  // 动态更新SEO meta标签（包含OG图片）
  const updateSeoMetaWithOgImage = (ogImageUrl: string) => {
    if (!pkData.value?.researcher1 || !pkData.value?.researcher2) return

    const researcher1Name = pkData.value.researcher1.name || 'Researcher 1'
    const researcher2Name = pkData.value.researcher2.name || 'Researcher 2'

    const title = `${researcher1Name} vs ${researcher2Name} - Scholar Comparison | DINQ`
    const description = `Compare researchers ${researcher1Name} and ${researcher2Name}. Analyze their academic achievements, publications, citations, and research impact.`

    // 获取当前域名
    const currentDomain = getSiteUrl()
    const pageUrl = `${currentDomain}/compare?researcher1=${encodeURIComponent(researcher1Name)}&researcher2=${encodeURIComponent(researcher2Name)}`

    useSeoMeta({
      title,
      description,
      ogTitle: title,
      ogDescription: description,
      ogImage: ogImageUrl,
      ogUrl: pageUrl,
      twitterCard: 'summary_large_image',
      twitterTitle: title,
      twitterDescription: description,
      twitterImage: ogImageUrl,
    })

    // 额外使用 useHead 确保 meta 标签被正确更新（与GitHub比较页面保持一致）
    useHead({
      meta: [
        { property: 'og:image', content: ogImageUrl, key: 'og:image' },
        { name: 'twitter:image', content: ogImageUrl, key: 'twitter:image' },
        { property: 'og:url', content: pageUrl, key: 'og:url' }
      ]
    })

    // 简化的客户端确认（保留日志用于调试）
    if (import.meta.client) {
      console.log('Scholar比较页面 meta 标签已更新:', {
        ogImage: ogImageUrl,
        twitterImage: ogImageUrl,
        ogUrl: pageUrl
      })
    }
  }

  // 检查是否已有Scholar比较页面OG图片
  const checkExistingScholarCompareOgImage = async (researcher1: any, researcher2: any) => {
    try {
      const researcher1Id = researcher1?.scholarId || researcher1?.scholar_id
      const researcher2Id = researcher2?.scholarId || researcher2?.scholar_id

      if (!researcher1Id || !researcher2Id) return

      const response = await $fetch(`/api/scholar/compare-og-image/${researcher1Id}/${researcher2Id}`)
      if (response.success && response.ogImageUrl) {
        ogImageUrl.value = response.ogImageUrl
        ogImageGenerated.value = true
        console.log('找到已有的Scholar比较页面OG图片:', response.ogImageUrl)

        // 如果找到已有图片，也需要更新meta标签
        updateSeoMetaWithOgImage(response.ogImageUrl)
      }
    } catch (error) {
      console.log('未找到已有的Scholar比较页面OG图片，将在分析完成后生成')
    }
  }

  const items = [
      {
        label: 'Github Stars',
        value: 4328,
      },
      {
        label: 'Github Stars',
        value: 4328,
      },
      {
        label: 'Github Stars',
        value: 4328,
      },
      {
        label: 'Github Stars',
        value: 4328,
      },
      {
        label: 'Github Stars',
        value: 4328,
      },
    ];

  const user = {
    name: 'Daiheng Gao',
    avatar: '/@/assets/image/avator.png',
    role: 'OpenAI-Researcher',
    papers: 1177,
    citations: 1177,
  }

  const stats = {
    firstAuthor: 99,
    total: 177,
    citation: 37,
  }

  const roleModel = {
    name: 'Marc Andreessen',
    avatar: '/marc.jpg',
    title: 'OpenAI-Researcher',
    achievement: 'Image transformer (2018)',
  }

  // demo
  const initData = {
    researcherInfo: {
      name: 'Niki Parmar',
      abbreviatedName: 'N Parmar',
      affiliation: 'Co-Founder at Essential AI',
      email: '',
      researchFields: ['Machine Learning', 'Deep Learning'],
      totalCitations: 199702,
      citations5y: 191401,
      hIndex: 43,
      hIndex5y: 43,
      yearlyCitations: {
        '2018': 1492,
        '2019': 5805,
        '2020': 11524,
        '2021': 20460,
        '2022': 31288,
        '2023': 45964,
        '2024': 60728,
        '2025': 21425,
      },
      scholarId: 'q2YXPSgAAAAJ',
      avatar: 'https://api.dinq.io/images/icon/avatar/0.png',
      description:
        'Niki Parmar, seeking elegant simplicity within complex systems. Transforming data into insight through focused, mindful presence.',
    },
    dataBlocks: {
      publicationStats: {
        blockTitle: 'Papers',
        totalPapers: 95,
        totalCitations: 199702,
        hIndex: 43,
        yearlyCitations: {
          '2018': 1492,
          '2019': 5805,
          '2020': 11524,
          '2021': 20460,
          '2022': 31288,
          '2023': 45964,
          '2024': 60728,
          '2025': 21425,
        },
        yearlyPapers: {
          '2016': 1,
          '2017': 31,
          '2018': 13,
          '2019': 5,
          '2020': 6,
          '2021': 8,
          '2022': 7,
          '2023': 5,
          '2024': 6,
          '2025': 2,
        },
      },
      publicationInsight: {
        blockTitle: 'Insight',
        totalPapers: 95,
        topTierPapers: 34,
        firstAuthorPapers: 2,
        firstAuthorCitations: 2447,
        totalCoauthors: 105,
        lastAuthorPapers: 6,
        conferenceDistribution: {
          CVPR: 3,
          ICML: 3,
          NeurIPS: 12,
          Others: 29,
        },
      },
      roleModel: {
        blockTitle: 'Role Model',
        found: true,
        name: 'Niki Parmar',
        institution: 'Co-Founder at Essential AI',
        position: 'Established Researcher',
        photoUrl: 'https://api.dinq.io/images/icon/avatar/0.png',
        achievement: 'Image transformer (2018)',
        similarityReason:
          'Congrats! You are already your own hero! Your unique research path and contributions have established you as a notable figure in your field.',
        isSelf: true,
      },
      closestCollaborator: {
        blockTitle: 'Closest Collaborator',
        fullName: 'ashish vaswani',
        affiliation: 'Essential AI',
        researchInterests: [],
        scholarId: '',
        coauthoredPapers: 46,
        avatar: 'https://api.dinq.io/images/icon/advisor.png',
        bestCoauthoredPaper: {
          title: 'Attention is all you need',
          year: 2017,
          venue: 'NeurIPS',
          fullVenue: 'NeurIPS 2017',
          citations: 181420,
        },
        connectionAnalysis: null,
      },
      estimatedSalary: {
        blockTitle: 'Estimated Salary',
        earningsPerYearUSD: null,
        levelEquivalency: {
          us: null,
          cn: null,
        },
        reasoning: null,
      },
      researcherCharacter: {
        blockTitle: 'Researcher Character',
        depthVsBreadth: 5,
        theoryVsPractice: 5,
        soloVsTeamwork: 5,
        justification: "Based on the researcher's publication record and citation metrics.",
      },
      representativePaper: {
        blockTitle: 'Representative Paper',
        title: 'Attention is all you need',
        year: 2017,
        venue: 'NeurIPS 2017',
        fullVenue: 'Advances in neural information processing systems 30, 2017',
        citations: 181420,
        authorPosition: 3,
        paper_news: {
          url: 'https://newsletter.theaiedge.io/p/attention-is-all-you-need-the-original',
          date: '2025-02-12',
          news: 'Attention Is All You Need: The Original Transformer Architecture',
          description:
            "A newsletter chapter from 'The AI Edge' examining the original Transformer architecture, including detailed explanations of self-attention mechanisms, multi-head attention layers, and the impact of this groundbreaking paper on modern AI development.",
          is_fallback: false,
        },
      },
      criticalReview: {
        blockTitle: 'Roast',
        evaluation:
          'Niki Parmar is a distinguished researcher in the fields of machine learning and deep learning, with a remarkable h-index of 43, demonstrating significant impact through highly-cited work, particularly the seminal paper "Attention is all you need." This work alone has garnered an astonishing 181,420 citations, underscoring their profound influence on the field.\n\nHowever, the researcher\'s citation growth rate indicates a slight decline, suggesting a potential need to diversify research topics or enhance engagement with emerging trends. Additionally, while their collaborative efforts are commendable, increasing first-author publications could further solidify their leadership in the field.\n\nWith their strong foundation and balanced approach to research, Niki Parmar is well-poised to continue making groundbreaking contributions to AI.',
      },
    },
    configInfo: {
      comment: 'Placeholder for bottom/page configuration data',
    },
  }

  // 静态数据
  const staticData = {
    researcher1: {
      name: 'Xingchao Liu',
      affiliation: 'DeepSeek AI',
      research_fields: [
        'Deep Learning',
        'Natural Language Processing',
        'Computer Vision',
        'Robotics',
      ],
      scholar_id: 'VOTVE0UAAAAJ',
      total_citations: 3867, //first_author_citations
      h_index: 22,
      top_tier_papers: 16,
      first_author_papers: 10,
      first_author_citations: 1956,
      most_cited_paper: {
        year: '2022',
        title: 'Flow straight and fast: Learning to generate and transfer data with rectified flow',
        venue: 'arXiv preprint arXiv:2209.03003, 2022 2022',
        authors: ['X Liu', 'C Gong', 'Q Liu'],
        citations: 857,
      },
      paper_evaluation:
        'This paper is significant for its innovative approach to data generation and transfer using rectified flow, evidenced by its substantial citation count of 857.',
      avatar: 'https://api.dinq.io/images/icon/avatar/6.png',
      evaluation:
        'Xingchao Liu has made impressive strides in Machine Learning research, with a notable H-index of 22 and 3867 total citations. Their focus on top-tier venues like NeurIPS and impactful paper on data generation showcases their commitment to quality. However, to further enhance their profile, increasing first-author and corresponding author papers could elevate their visibility. Diversifying collaboration networks beyond ByteDance Inc. and exploring interdisciplinary research avenues may foster fresh perspectives. With consistent publication output and a solid citation growth rate, Liu shows promise in shaping the future of ML. Encouraging them to maintain this momentum and venture into new research domains could lead to even greater achievements.',
    },
    researcher2: {
      name: 'Qiang Liu',
      affiliation: 'Associate Professor of Computer Science, UT Austin',
      research_fields: [
        'Machine learning',
        'graphical models',
        'approximate inference',
        'reinforcement learning',
        'crowdsourcing',
      ],
      scholar_id: 'XEx1fZkAAAAJ',
      total_citations: 13191,
      h_index: 63,
      top_tier_papers: 37,
      first_author_papers: 20,
      first_author_citations: 4253,
      most_cited_paper: {
        title: 'Stein variational gradient descent: A general purpose Bayesian inference algorithm',
        year: '2016',
        venue: 'NeurIPS 2016',
        citations: 1332,
        authors: ['Q Liu', 'D Wang'],
      },
      paper_evaluation:
        'The paper introduces a groundbreaking Bayesian inference algorithm, significantly influencing computational statistics, evidenced by its 1332 citations since NeurIPS 2016.',
      avatar: 'https://api.dinq.io/images/icon/avatar/9.png',
      evaluation:
        'Qiang Liu has made remarkable contributions to machine learning and Bayesian inference, evident from an impressive H-index of 63 and 13191 total citations. His work on Stein variational gradient descent stands out with 1332 citations. However, there is room for improvement in increasing the citation growth rate, which currently shows a decline. Diversifying publication venues beyond Arxiv and focusing on high-impact journals could enhance visibility. Collaborating with a wider network might also foster new research directions. With continued dedication to innovation and strategic publication strategies, Liu is poised to further elevate his research impact and productivity.',
    },
    roast:
      "Xingchao Liu's impressive strides in Machine Learning are overshadowed by Qiang Liu's towering H-index and citation count, making Liu's achievements seem like a mere foothill compared to Qiang's towering peaks of academic excellence.",
  }

  const setDefaultImage = (event: Event) => {
    const target = event.target as HTMLImageElement
    target.src = '/image/avator.png'
  }

  // 激活码调试;
  // showInviteModal.value = true;

  watch(limitInfo, data => {
    if (data && data.errorType === 'limit') {
      showInviteModal.value = true
      inviteError.value = ''
      inviteLoading.value = false
    }
  })

  // 设置初始 SEO meta（在服务端渲染时生效）
  if (route.query.researcher1 && route.query.researcher2) {
    const researcher1Name = route.query.researcher1 as string
    const researcher2Name = route.query.researcher2 as string

    const initialTitle = `${researcher1Name} vs ${researcher2Name} - Scholar Comparison | DINQ`
    const initialDescription = `Compare researchers ${researcher1Name} and ${researcher2Name}. Analyze their academic achievements, publications, citations, and research impact.`

    // 尝试从URL参数中提取Scholar ID，如果参数本身就是Scholar ID
    const possibleScholar1Id = extractScholarId(researcher1Name)
    const possibleScholar2Id = extractScholarId(researcher2Name)

    let initialOgImageUrl = 'https://dinq.io/image/scholar-compare-default.png' // 默认fallback

    // 如果能提取到Scholar ID，生成可预测的OG图片URL
    if (possibleScholar1Id && possibleScholar2Id) {
      const fileName = `scholar-compare-${possibleScholar1Id}-vs-${possibleScholar2Id}-latest.png`
      initialOgImageUrl = `https://dinq-share-og.s3.us-east-2.amazonaws.com/shares/${fileName}`
    }

    useSeoMeta({
      title: initialTitle,
      description: initialDescription,
      ogTitle: initialTitle,
      ogDescription: initialDescription,
      ogImage: initialOgImageUrl,
      ogType: 'website',
      ogUrl: `https://dinq.io/compare?researcher1=${encodeURIComponent(researcher1Name)}&researcher2=${encodeURIComponent(researcher2Name)}`,
      twitterCard: 'summary_large_image',
      twitterTitle: initialTitle,
      twitterDescription: initialDescription,
      twitterImage: initialOgImageUrl,
    })
  }

  // 当组件挂载时，发起比较请求或使用静态数据
  onMounted(() => {
    if (route.query.researcher1 && route.query.researcher2) {
      // 重置OG图片状态
      ogImageGenerated.value = false
      ogImageUrl.value = ''

      // 检查是否已有OG图片（基于URL参数的初步检查）
      checkExistingScholarCompareOgImage(
        { name: route.query.researcher1 },
        { name: route.query.researcher2 }
      )

      // 如果有查询参数，尝试从API获取数据
      connectWithObj(route.query, '/api/scholar-pk', { Userid: currentUser.value?.uid || '' })
    } else {
      // 否则使用静态数据, 调试使用;
      // pkData.value = staticData
      // loading.value = false
    }
  })

  // 处理左侧搜索框的比较请求（左侧研究者固定，用户输入右侧研究者）
  const handleLeftCompare = (query?: string) => {
    const newResearcher = query || searchInputRef1.value?.searchValue
    
    if (!newResearcher?.trim() || !pkData.value?.researcher1?.name) return
    
    const researcher1Name = pkData.value.researcher1.name
    console.log('handleLeftCompare', researcher1Name, newResearcher)

    // 更新路由参数：左侧研究者保持不变，右侧研究者使用用户输入
    router.replace({
      path: '/compare',
      query: {
        researcher1: researcher1Name,
        researcher2: newResearcher.trim(),
      },
    })

    // 显示分析中弹窗
    loading.value = true
    isLoadingJson.value = false // 重置 JSON 加载状态
    pkData.value = null

    connectWithObj(
      {
        researcher1: researcher1Name,
        researcher2: newResearcher.trim(),
      },
      '/api/scholar-pk',
      {
        Userid: currentUser.value?.uid || '',
      }
    )
  }

  // 处理右侧搜索框的比较请求（右侧研究者固定，用户输入左侧研究者）
  const handleRightCompare = (query?: string) => {
    const newResearcher = query || searchInputRef2.value?.searchValue
    
    if (!newResearcher?.trim() || !pkData.value?.researcher2?.name) return
    
    const researcher2Name = pkData.value.researcher2.name
    console.log('handleRightCompare', newResearcher, researcher2Name)

    // 更新路由参数：左侧研究者使用用户输入，右侧研究者保持不变
    router.replace({
      path: '/compare',
      query: {
        researcher1: newResearcher.trim(),
        researcher2: researcher2Name,
      },
    })

    // 显示分析中弹窗
    loading.value = true
    isLoadingJson.value = false // 重置 JSON 加载状态
    pkData.value = null

    connectWithObj(
      {
        researcher1: newResearcher.trim(),
        researcher2: researcher2Name,
      },
      '/api/scholar-pk',
      {
        Userid: currentUser.value?.uid || '',
      }
    )
  }

  // 处理底部搜索框的比较请求
  const handleCompare = () => {
    console.log('handleCompare', researcher1Input.value, researcher2Input.value)
    if (!researcher1Input.value || !researcher2Input.value) return

    // 更新路由参数
    router.replace({
      path: '/compare',
      query: {
        researcher1: researcher1Input.value,
        researcher2: researcher2Input.value,
      },
    })

    // 显示分析中弹窗
    loading.value = true
    isLoadingJson.value = false // 重置 JSON 加载状态
    pkData.value = null

    connectWithObj(
      {
        researcher1: researcher1Input.value,
        researcher2: researcher2Input.value,
      },
      '/api/scholar-pk',
      {
        Userid: currentUser.value?.uid || '',
      }
    )
  }

  // 监听pkData变化
  watch(reportDataInfo, newData => {
    if (newData) {
      fetchReportData(newData.jsonUrl)
    }
  })

  const fetchReportData = (url: string) => {
    let processedUrl = url
    isLoadingJson.value = true // 开始加载 JSON 数据

    try {
      if (url.startsWith('http')) {
        const urlObj = new URL(url)
        const currentDomain = getSiteUrl()
        const isLocalDomain = [
          'localhost',
          '127.0.0.1',
          '0.0.0.0',
          '::1',
          /^192\.168\.\d{1,3}\.\d{1,3}$/,
          /^10\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,
          /^172\.(1[6-9]|2[0-9]|3[0-1])\.\d{1,3}\.\d{1,3}$/,
        ].some(pattern => {
          if (typeof pattern === 'string') {
            return urlObj.hostname === pattern
          } else if (pattern instanceof RegExp) {
            return pattern.test(urlObj.hostname)
          }
          return false
        })

        if (isLocalDomain && process.client) {
          const currentDomain = window.location.origin
          processedUrl = `${currentDomain}${urlObj.pathname}${urlObj.search}${urlObj.hash}`
          console.log(`Converted local URL from ${url} to ${processedUrl}`)
        } else {
          console.log('URL is public, using original URL')
        }
      }
    } catch (e) {
      console.warn('URL processing error:', e)
      processedUrl = url
    }

    console.log('Fetching report data from:', processedUrl)
    fetch(processedUrl, {
      headers: {
        userid: currentUser.value?.uid || '',
        'Content-Type': 'application/json',
      },
    })
      .then(response => {
        if (!response.ok) {
          throw new Error(`Failed to fetch report data: ${response.status}`)
        }
        return response.json()
      })
      .then(data => {
        console.log('Report data fetched successfully')
        pkData.value = data

        // Compare页面作为过境页面：获得scholarId后重定向到Scholar_Compare页面
        if (pkData.value) {
          const scholar1Id = pkData.value.researcher1?.scholarId || pkData.value.researcher1?.scholar_id
          const scholar2Id = pkData.value.researcher2?.scholarId || pkData.value.researcher2?.scholar_id

          if (scholar1Id && scholar2Id) {
            // 缓存比较结果到sessionStorage
            if (import.meta.client) {
              try {
                sessionStorage.setItem('scholarCompareResult', JSON.stringify(pkData.value))
                console.log('Scholar比较结果已缓存，准备重定向到Scholar_Compare页面')
              } catch (error) {
                console.warn('缓存Scholar比较结果失败:', error)
              }
            }

            // 重定向到Scholar_Compare页面
            router.replace({
              path: '/scholar_compare',
              query: {
                user1: scholar1Id,
                user2: scholar2Id,
                from: 'compare'
              }
            })
            return
          } else {
            console.warn('未获得scholarId，无法重定向到Scholar_Compare页面，继续在当前页面显示结果')
          }

          // 如果无法获取scholarId，继续使用当前页面（fallback）
          // 先检查是否已有OG图片
          if (pkData.value.researcher1 && pkData.value.researcher2) {
            checkExistingScholarCompareOgImage(pkData.value.researcher1, pkData.value.researcher2)
          }

          // 更新SEO meta（会使用已有的ogImageUrl.value或生成可预测URL）
          updateScholarCompareSeoMeta(pkData.value)

          // 自动生成OG图片（如果还没有的话）
          nextTick(() => {
            if (pkData.value?.researcher1 && pkData.value?.researcher2 && !ogImageGenerated.value) {
              generateScholarCompareOgImage(pkData.value.researcher1, pkData.value.researcher2)
            }
          })
        }
      })
      .catch(error => {
        console.error('Error fetching report data:', error)
        // 如果获取 JSON 数据失败，清空 pkData 以显示兜底内容
        pkData.value = null
      })
      .finally(() => {
        isLoadingJson.value = false // 结束加载状态
        loading.value = false // 结束整体加载状态
      })
  }

  // 监听路由变化，重置状态
  watch(
    route,
    () => {
      if (!route.query.researcher1 || !route.query.researcher2) {
        pkData.value = null
        loading.value = false
        isLoadingJson.value = false
      }
    },
    { immediate: true }
  )

  function onShowWaitingListModal() {
    showInviteModal.value = false
    showWaitingListModal.value = true
  }
  function onBackToInviteCode() {
    showWaitingListModal.value = false
    showInviteModal.value = true
  }

  async function handleSubmitActivationCode(code: string) {
    inviteLoading.value = true
    inviteError.value = ''
    try {
      const res = await submitActivationCode(
        '/api/activation-codes/use',
        { code },
        { headers: { Userid: currentUser.value?.uid || '' } }
      )
      if (res.data?.success) {
        showInviteModal.value = false
        inviteSuccess.value = true
        setTimeout(() => {
          inviteSuccess.value = false
        }, 2000)
      } else {
        inviteError.value = 'Invalid invite code. Please check and try again.'
      }
    } catch (e) {
      inviteError.value = 'Invalid invite code. Please check and try again.'
    } finally {
      inviteLoading.value = false
    }
  }

  function goHome() {
    router.replace('/analysis')
  }
</script>

<style scoped>
  .px-30 {
    padding-left: 120px;
    padding-right: 120px;
  }

  .w-15 {
    width: 3.75rem;
  }

  .h-15 {
    height: 3.75rem;
  }

  .clash-display {
    font-family: "Poppins", sans-serif;
  }

  .text-primary-100 {
    color: #c69279;
  }

  .text-15px {
    font-size: 15px;
  }

  /* 输入框聚焦时的样式 */
  input:focus {
    box-shadow: 0 0 0 2px rgba(198, 146, 121, 0.2);
  }

  .i-carbon:warning-alt {
    width: 2rem;
    height: 2rem;
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }

  @keyframes progress {
    0% {
      width: 0%;
    }
    100% {
      width: 100%;
    }
  }

  .animate-progress {
    animation: progress 2s ease-in-out infinite;
  }
  .fw {
    /* "Full Width" 的缩写 */
    width: 60%;
  }

  /* 控制元素靠右 */
  .sty_f_r_end {
    display: flex;
    justify-content: flex-end;
  }

  .compare-page-bg {
    background-image: url('~/assets/image/bgimg.png');
    /* background-size: 100%; */
  }

  .dark .compare-page-bg {
    background-image: url('~/assets/image/bgimgdark_1.png');
  }
</style>
