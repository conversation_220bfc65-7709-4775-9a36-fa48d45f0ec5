<template>
  <div class="px-4 pt-20" :class="{ 'pt-15': isMobile }">
    <!-- 顶部 Banner -->
    <div
      class="h-50 bg-blue-600 text-white text-xl font-bold p-4 fx-cer justify-center flex-col rounded-lg mb-6"
    >
      AI TRANSFER WINDOW
    </div>

    <div class="p-2 rounded-lg shadow-md bg-white dark:bg-transparent">
      <!-- 标题 + 筛选下拉 -->
      <div class="p-4 flex items-center justify-between">
        <h2 class="text-lg font-semibold">Latest Transfer Updates</h2>
        <select
          v-model="selectedSort"
          @change="applySort"
          class="border px-2 py-1 rounded text-sm dark:bg-gray-800 dark:text-white dark:border-gray-600"
        >
          <option value="recent">Sort by Time</option>
          <option value="hot">Sort by Hot</option>
        </select>
      </div>

      <!-- 卡片列表 -->
      <div ref="scrollContainer" @scroll="onScroll" class="px-4 space-y-4 overflow-y-auto h-[58vh]">
        <!-- 卡片内容渲染（avatar + 描述 + 浏览量 + 时间） -->
        <div
          v-for="card in sortedCards"
          :key="card.id"
          class="p-4 border rounded-3 shadow cursor-pointer hover:bg-gray-100 dark:bg-gray-800 dark:border-gray-600 dark:hover:bg-gray-700 flex justify-between items-center"
          @click="() => openModal(card)"
        >
          <!-- 左侧内容 -->
          <div class="flex-1">
            <div class="text-sm text-gray-700 dark:text-gray-300 mb-2">
              <span v-html="card.descriptionHtml" />
            </div>
            <div class="flex items-center text-xs text-gray-600 dark:text-gray-400 gap-4">
              <div class="flex items-center gap-1">👁 {{ card.views }}</div>
              <div>{{ formatDate(card.time) }}</div>
            </div>
          </div>

          <!-- 头像 -->
          <img
            :src="card.avatarUrl"
            alt="avatar"
            class="w-10 h-10 rounded-full border border-gray-300 object-cover ml-4"
          />
        </div>

        <div v-if="loading" class="text-center text-gray-400">Loading...</div>
      </div>
    </div>

    <!-- 弹窗 -->
    <div v-if="showModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div
        class="bg-white dark:bg-gray-900 text-black dark:text-white p-8 rounded-lg max-w-md w-full relative"
      >
        <div
          class="h-[200px] overflow-auto whitespace-pre-wrap font-mono text-sm border rounded p-2 dark:border-gray-700 font-Poppins"
        >
          {{ animatedText }}
        </div>

        <div class="flex justify-start gap-4 mt-4">
          <button class="text-blue-600 hover:underline bg-transparent" @click="refreshText">
            <img src="~/assets/image/refresh 1.svg" alt="" class="w-4 h-4 btn-icon-light" />
            <img src="~/assets/image/refresh2.svg" alt="" class="w-4 h-4 btn-icon-dark" />
          </button>
          <button class="text-blue-600 hover:underline bg-transparent" @click="copyText">
            <img src="~/assets/image/Copy.svg" alt="" class="w-4 h-4" />
          </button>
        </div>

        <div class="absolute top-2 right-3 cursor-pointer text-xl" @click="showModal = false">
          <img src="~/assets/image/close-middle.svg" alt="" class="w-5 h-5" />
        </div>
      </div>
    </div>
    <Notification ref="notifier" position="center" />
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'
  import Notification from '@/components/SearchCard/Notification.vue'

  const scrollContainer = ref<HTMLElement | null>(null)
  const notifier = ref()
  const isMobile = ref(false)
  onMounted(() => {
    isMobile.value = window.innerWidth < 768
  })

  const notify = message => {
    notifier.value?.add(message)
  }

  interface Card {
    id: string
    avatarUrl: string
    description: string
    views: number
    time: number
    descriptionHtml?: string // 由前端解析后添加
  }

  const cards = ref<Card[]>([])
  const page = ref(1)
  const loading = ref(false)

  const selectedSort = ref<'recent' | 'hot'>('recent')

  const sortedCards = computed(() => {
    if (selectedSort.value === 'hot') {
      return [...cards.value].sort((a, b) => b.heat - a.heat)
    }
    return [...cards.value].sort((a, b) => b.time - a.time)
  })

  const applySort = () => {
    console.log('Sort changed to:', selectedSort.value)
  }

  const fetchMore = async () => {
    if (loading.value) return
    loading.value = true
    await new Promise(r => setTimeout(r, 500))

    const now = Date.now()
    const newCards = Array.from({ length: 10 }, (_, i) => {
      const from = 'Tencent'
      const to = 'ByteDance'
      const position = 'Lead ML Engineer'
      const salary = '2.2 Million RMB'
      const user = `User ${page.value}-${i}`

      const description = `${user} transferred from ${from} to ${to}, taking the role of ${position}, with a total package exceeding ${salary}`

      return {
        id: `${page.value}-${i}`,
        avatarUrl: `https://i.pravatar.cc/150?u=${page.value}-${i}`,
        description,
        views: Math.floor(Math.random() * 1000) + 100,
        time: now - Math.random() * 10000000,
        descriptionHtml: formatDescription(description), // 高亮 HTML
      }
    })

    cards.value.push(...newCards)
    page.value++
    loading.value = false
  }

  const onScroll = () => {
    const el = scrollContainer.value
    if (!el) return

    const nearBottom = el.scrollTop + el.clientHeight >= el.scrollHeight - 100
    if (nearBottom) fetchMore()
  }

  const showModal = ref(false)
  const selectedCard = ref<Card | null>(null)
  const fullText = ref('')
  const animatedText = ref('')
  const typingIndex = ref(0)
  let interval: any = null

  const fetchDetailText = async (card: Card) => {
    await new Promise(r => setTimeout(r, 300))
    return `🚀 ${card.user} transferred from ${card.from} to ${card.to}.\n\nThis transition marks a significant milestone in their career. We wish them success! 🎉`
  }

  const animateText = (text: string) => {
    animatedText.value = ''
    typingIndex.value = 0
    clearInterval(interval)
    interval = setInterval(() => {
      if (typingIndex.value < text.length) {
        animatedText.value += text[typingIndex.value]
        typingIndex.value++
      } else {
        clearInterval(interval)
      }
    }, 30)
  }

  const openModal = async (card: Card) => {
    selectedCard.value = card
    showModal.value = true
    fullText.value = await fetchDetailText(card)
    animateText(fullText.value)
  }

  const refreshText = async () => {
    if (!selectedCard.value) return
    fullText.value = await fetchDetailText(selectedCard.value)
    animateText(fullText.value)
  }

  const copyText = async () => {
    await navigator.clipboard.writeText(fullText.value)
    notify('Text copied')
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('en-US', {
      dateStyle: 'long',
      timeStyle: 'short',
    })
  }

  onMounted(() => {
    fetchMore()
  })

  // 描述高亮
  function formatDescription(description: string): string {
    let html = description

    // 高亮公司名（略微优化避免职位等词误伤）
    html = html.replace(/\b([A-Z][a-zA-Z0-9]{2,})\b/g, match => {
      const ignore = ['User', 'This', 'With', 'From', 'To', 'The', 'And']
      if (ignore.includes(match)) return match
      return `<span class="text-blue-600 font-medium">${match}</span>`
    })

    // 高亮职位关键词
    html = html.replace(
      /\b(Senior\s+)?(AI|ML|Data)?\s*(Engineer|Researcher|Scientist|Manager|Developer|Analyst|Leader)\b/gi,
      match => `<span class="text-gray-700 dark:text-gray-300 font-semibold">${match}</span>`
    )

    // ✅ 高亮薪资：匹配"exceeding"/"offering"后紧跟的完整金额（含数字）
    html = html.replace(
      /\b(exceeding|offering|worth)\s+([\d,.]+\s*(Million|Billion)?\s*(RMB|USD|\$|K|万|¥)?)\b/gi,
      (match, prefix, salary) => {
        return `${prefix} <span class="text-pink-600 font-semibold">${salary}</span>`
      }
    )

    return html
  }
</script>

<style scoped>
  /* 按钮图标控制 */
  .btn-icon-dark {
    display: none;
  }

  .dark .btn-icon-light {
    display: none;
  }

  .dark .btn-icon-dark {
    display: inline-block;
  }

  .font-Poppins {
    font-family: 'Poppins', sans-serif;
  }
</style>
